/**
 * LLM Modeling Metrics Dashboard JavaScript Application
 */

class LLMMetricsDashboard {
    constructor() {
        this.apiBaseUrl = '/api';
        this.websocket = null;
        this.currentAnalysisId = null;
        this.supportedModels = {};
        this.memoryControls = null;
        this.hardwareSelector = null;
        this.selectedHardware = null;
        this.rooflineVisualizer = null;
        this.interactiveControls = null;
        this.advancedVisualizationControls = null;
        this.modelPresets = {
            'dense-comparison': [
                "meta-llama/Meta-Llama-3-8B-Instruct",
                "meta-llama/Meta-Llama-3-70B-Instruct",
                "meta-llama/Llama-2-7b-hf",
                "Qwen/Qwen2-7B"
            ],
            'moe-comparison': [
                "deepseek-ai/DeepSeek-V3",
                "deepseek-ai/DeepSeek-v2-lite",
                "moonshotai/Kimi-K2-Instruct"
            ]
        };

        this.init();
    }

    async init() {
        try {
            console.log('Initializing LLM Metrics Dashboard...');
            console.log('jQuery available:', typeof $ !== 'undefined');
            console.log('Select2 available:', typeof $.fn.select2 !== 'undefined');

            // Check if preset buttons exist in DOM
            const presetButtons = document.querySelectorAll('[data-preset]');
            console.log('Preset buttons found during init:', presetButtons.length);

            await this.loadSupportedModels();
            this.initializeComponents();
            await this.initializeMemoryControls();
            await this.initializeHardwareSelector();
            await this.initializeRooflineVisualizer();
            await this.initializeInteractiveControls();
            await this.initializeAdvancedVisualizationControls();
            await this.initializeTimingComponents();
            this.bindEvents();
            this.initializeSectionSwitching();
            console.log('Dashboard initialized successfully');

            // Double-check preset buttons after initialization
            const presetButtonsAfter = document.querySelectorAll('[data-preset]');
            console.log('Preset buttons found after init:', presetButtonsAfter.length);
        } catch (error) {
            console.error('Failed to initialize dashboard:', error);
            console.error('Error details:', error.stack);
            this.showError('Failed to initialize dashboard. Please refresh the page.');
        }
    }

    async loadSupportedModels() {
        try {
            console.log('Loading supported models...');
            const response = await fetch(`${this.apiBaseUrl}/models/supported`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            this.supportedModels = data;
            console.log('Supported models loaded:', data);

            // Populate model select dropdown
            this.populateModelSelect();
        } catch (error) {
            console.error('Error loading supported models:', error);
            throw error;
        }
    }

    populateModelSelect() {
        console.log('populateModelSelect called');
        const modelSelect = $('#modelSelect');
        console.log('modelSelect element found:', modelSelect.length > 0);

        modelSelect.empty();

        // Add options grouped by architecture
        Object.entries(this.supportedModels.architecture_info).forEach(([arch, info]) => {
            if (info.examples && info.examples.length > 0) {
                console.log(`Adding ${arch} with ${info.examples.length} models`);
                const optgroup = $(`<optgroup label="${arch.toUpperCase()}">`);

                info.examples.forEach(model => {
                    optgroup.append(`<option value="${model}">${model}</option>`);
                });

                modelSelect.append(optgroup);
            }
        });

        console.log('Options added, initializing Select2...');

        try {
            // Initialize Select2
            modelSelect.select2({
                theme: 'bootstrap-5',
                placeholder: 'Search and select models or enter custom model names...',
                allowClear: true,
                closeOnSelect: false,
                tags: true,
                tokenSeparators: [',', ' ']
            });

            console.log('Select2 initialized successfully');

            // Test if Select2 container exists
            setTimeout(() => {
                const select2Container = $('.select2-container');
                console.log('Select2 containers found:', select2Container.length);
                if (select2Container.length > 0) {
                    console.log('Select2 container HTML:', select2Container[0].outerHTML.substring(0, 200));
                }
            }, 100);

        } catch (error) {
            console.error('Select2 initialization failed:', error);
        }

        console.log('Model select populated with', Object.keys(this.supportedModels.architecture_info).length, 'architectures');
    }

    initializeComponents() {
        // Initialize parallel configuration collapse
        const enableParallel = document.getElementById('enableParallel');
        const parallelConfig = document.getElementById('parallelConfig');

        if (enableParallel && parallelConfig) {
            enableParallel.addEventListener('change', () => {
                if (enableParallel.checked) {
                    $(parallelConfig).collapse('show');
                } else {
                    $(parallelConfig).collapse('hide');
                }
            });
        }

        // Initialize hardware analysis toggle
        const enableHardwareAnalysis = document.getElementById('enableHardwareAnalysis');
        const hardwareSelectorContainer = document.getElementById('hardwareSelectorContainer');

        if (enableHardwareAnalysis && hardwareSelectorContainer) {
            enableHardwareAnalysis.addEventListener('change', () => {
                if (enableHardwareAnalysis.checked) {
                    $(hardwareSelectorContainer).collapse('show');
                } else {
                    $(hardwareSelectorContainer).collapse('hide');
                }
            });
        }
    }

    /**
     * Initialize memory controls
     */
    async initializeMemoryControls() {
        try {
            // Initialize integrated memory controls
            this.initializeIntegratedMemoryControls();

            // If MemoryControls class is available, use it for advanced features
            if (typeof MemoryControls !== 'undefined') {
                this.memoryControls = new MemoryControls(this);
                // Don't call init() as we're using integrated controls

                // Listen for memory controls state changes
                document.addEventListener('memoryControlsChange', (event) => {
                    this.onMemoryControlsChange(event.detail.property, event.detail.value, event.detail.state);
                });
            } else {
                console.warn('MemoryControls class not available, using integrated controls only');
            }
        } catch (error) {
            console.error('Failed to initialize memory controls:', error);
            // Continue without memory controls
        }
    }

    /**
     * Initialize hardware selector component
     */
    async initializeHardwareSelector() {
        try {
            console.log('Initializing hardware selector...');
            
            // Check if HardwareSelector class is available
            if (typeof HardwareSelector === 'undefined') {
                console.warn('HardwareSelector class not available');
                return;
            }

            // Initialize hardware selector
            this.hardwareSelector = new HardwareSelector('hardwareSelectorContainer', {
                apiBaseUrl: this.apiBaseUrl,
                showValidation: true,
                showSpecs: true,
                onHardwareChange: (hardware) => {
                    this.onHardwareSelectionChange(hardware);
                }
            });

            // Listen for hardware selection events
            document.addEventListener('hardwareSelectionChange', (event) => {
                this.handleHardwareSelectionChange(event.detail);
            });

            console.log('Hardware selector initialized successfully');
        } catch (error) {
            console.error('Failed to initialize hardware selector:', error);
            // Continue without hardware selector
        }
    }

    /**
     * Initialize roofline visualizer component
     */
    async initializeRooflineVisualizer() {
        try {
            console.log('Initializing roofline visualizer...');
            
            // Check if RooflineVisualizer class is available
            if (typeof RooflineVisualizer === 'undefined') {
                console.warn('RooflineVisualizer class not available');
                return;
            }

            // Initialize roofline visualizer
            this.rooflineVisualizer = new RooflineVisualizer('rooflineVisualizationContainer', {
                apiBaseUrl: this.apiBaseUrl,
                showControls: true,
                showLegend: true,
                enableZoom: true,
                enablePan: true,
                onOperatorClick: (dataset, index) => {
                    this.onRooflineOperatorClick(dataset, index);
                },
                onPrecisionChange: (precisions) => {
                    this.onRooflinePrecisionChange(precisions);
                }
            });

            console.log('Roofline visualizer initialized successfully');
        } catch (error) {
            console.error('Failed to initialize roofline visualizer:', error);
            // Continue without roofline visualizer
        }
    }

    /**
     * Initialize interactive controls component
     */
    async initializeInteractiveControls() {
        try {
            console.log('Initializing interactive controls...');
            
            // Check if InteractiveControls class is available
            if (typeof InteractiveControls === 'undefined') {
                console.warn('InteractiveControls class not available');
                return;
            }

            // Initialize interactive controls
            this.interactiveControls = new InteractiveControls('interactiveControlsContainer', {
                apiBaseUrl: this.apiBaseUrl,
                enableRealTimeUpdates: true,
                updateDelay: 500,
                onParameterChange: (parameters) => {
                    this.onInteractiveParameterChange(parameters);
                },
                onPrecisionChange: (precisionConfig) => {
                    this.onInteractivePrecisionChange(precisionConfig);
                },
                onPresetLoad: (presetKey, preset) => {
                    this.onInteractivePresetLoad(presetKey, preset);
                }
            });

            console.log('Interactive controls initialized successfully');
        } catch (error) {
            console.error('Failed to initialize interactive controls:', error);
            // Continue without interactive controls
        }
    }

    /**
     * Initialize advanced visualization controls component
     */
    async initializeAdvancedVisualizationControls() {
        try {
            console.log('Initializing advanced visualization controls...');
            
            // Check if AdvancedVisualizationControls class is available
            if (typeof AdvancedVisualizationControls === 'undefined') {
                console.warn('AdvancedVisualizationControls class not available');
                return;
            }

            // Initialize advanced visualization controls
            this.advancedVisualizationControls = new AdvancedVisualizationControls('advancedVisualizationControlsContainer', {
                apiBaseUrl: this.apiBaseUrl,
                enableExport: true,
                enableSharing: true,
                enableBookmarks: true,
                onFilterChange: (filters) => {
                    this.onVisualizationFilterChange(filters);
                },
                onGroupingChange: (grouping) => {
                    this.onVisualizationGroupingChange(grouping);
                },
                onExport: (type, format) => {
                    return this.onVisualizationExport(type, format);
                },
                onShare: (shareUrl, sessionData) => {
                    this.onVisualizationShare(shareUrl, sessionData);
                },
                onZoomReset: () => {
                    this.onVisualizationZoomReset();
                },
                onZoomToFit: () => {
                    this.onVisualizationZoomToFit();
                },
                onPersistLabelsChange: (persist) => {
                    this.onVisualizationPersistLabelsChange(persist);
                }
            });

            console.log('Advanced visualization controls initialized successfully');
        } catch (error) {
            console.error('Failed to initialize advanced visualization controls:', error);
            // Continue without advanced visualization controls
        }
    }

    /**
     * Initialize timing dashboard and optimization suggestions components
     */
    async initializeTimingComponents() {
        try {
            console.log('Initializing timing components...');
            
            // Check if timing components are available
            if (typeof TimingDashboard === 'undefined') {
                console.warn('TimingDashboard class not available');
            } else {
                console.log('TimingDashboard initialized successfully');
            }

            if (typeof OptimizationSuggestions === 'undefined') {
                console.warn('OptimizationSuggestions class not available');
            } else {
                console.log('OptimizationSuggestions initialized successfully');
            }

            console.log('Timing components initialization completed');
        } catch (error) {
            console.error('Failed to initialize timing components:', error);
            // Continue without timing components
        }
    }

    /**
     * Initialize integrated memory controls in the main dashboard
     */
    initializeIntegratedMemoryControls() {
        // Initialize memory state
        this.memoryState = {
            showTotalMemory: false,
            kvCacheDtype: 'fp16',
            minSequenceLength: 512,
            maxSequenceLength: 32768,
            supportedDtypes: ['fp16', 'bf16', 'fp32', 'int8']
        };

        // Bind memory control events
        this.bindMemoryControlEvents();

        // Load supported dtypes
        this.loadSupportedDtypes();

        // Initialize visibility
        this.updateMemoryControlsVisibility();
    }

    /**
     * Bind events for integrated memory controls
     */
    bindMemoryControlEvents() {
        // Memory toggle switch
        const memoryToggle = document.getElementById('showTotalMemoryToggle');
        if (memoryToggle) {
            memoryToggle.addEventListener('change', (e) => {
                this.memoryState.showTotalMemory = e.target.checked;
                this.updateMemoryControlsVisibility();
                this.handleMemoryToggleChange(e.target.checked);
            });
        }

        // KV cache dtype selector
        const kvDtypeSelect = document.getElementById('kvCacheDtype');
        if (kvDtypeSelect) {
            kvDtypeSelect.addEventListener('change', (e) => {
                this.memoryState.kvCacheDtype = e.target.value;
                this.handleKvDtypeChange(e.target.value);
            });
        }

        // Sequence length controls
        const minSeqLength = document.getElementById('minSequenceLength');
        const maxSeqLength = document.getElementById('maxSequenceLength');

        if (minSeqLength) {
            minSeqLength.addEventListener('input', (e) => {
                this.memoryState.minSequenceLength = parseInt(e.target.value);
                this.validateSequenceLengthRange();
            });
        }

        if (maxSeqLength) {
            maxSeqLength.addEventListener('input', (e) => {
                this.memoryState.maxSequenceLength = parseInt(e.target.value);
                this.validateSequenceLengthRange();
            });
        }

        // KV growth chart button
        const kvGrowthBtn = document.getElementById('showKvGrowthBtn');
        if (kvGrowthBtn) {
            kvGrowthBtn.addEventListener('click', () => {
                this.showKvGrowthChart();
            });
        }
    }

    /**
     * Load supported data types from API
     */
    async loadSupportedDtypes() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/memory/dtypes`);
            if (response.ok) {
                const data = await response.json();
                this.memoryState.supportedDtypes = data.dtypes || this.memoryState.supportedDtypes;
                this.updateKvDtypeOptions();
            }
        } catch (error) {
            console.warn('Could not load supported dtypes, using defaults:', error);
        }
    }

    /**
     * Update KV dtype selector options
     */
    updateKvDtypeOptions() {
        const kvDtypeSelect = document.getElementById('kvCacheDtype');
        if (!kvDtypeSelect) return;

        const currentValue = kvDtypeSelect.value;
        kvDtypeSelect.innerHTML = '';

        const dtypeNames = {
            'fp16': 'FP16 (16-bit float)',
            'bf16': 'BF16 (bfloat16)',
            'fp32': 'FP32 (32-bit float)',
            'int8': 'INT8 (8-bit integer)'
        };

        this.memoryState.supportedDtypes.forEach(dtype => {
            const option = document.createElement('option');
            option.value = dtype;
            option.textContent = dtypeNames[dtype] || dtype.toUpperCase();
            if (dtype === currentValue) {
                option.selected = true;
            }
            kvDtypeSelect.appendChild(option);
        });
    }

    /**
     * Update memory controls visibility based on toggle state
     */
    updateMemoryControlsVisibility() {
        const kvCacheConfig = document.getElementById('kvCacheConfig');
        if (!kvCacheConfig) return;

        if (this.memoryState.showTotalMemory) {
            $(kvCacheConfig).collapse('show');
        } else {
            $(kvCacheConfig).collapse('hide');
        }
    }

    /**
     * Enhanced sequence length range validation with user feedback
     */
    validateSequenceLengthRange() {
        const validationDiv = document.getElementById('sequenceLengthValidation');
        if (!validationDiv) return true;

        const min = this.memoryState.minSequenceLength;
        const max = this.memoryState.maxSequenceLength;

        validationDiv.innerHTML = '';

        const errors = [];
        const warnings = [];
        const info = [];

        // Enhanced validation rules (512-32768)
        if (min >= max) {
            errors.push('Minimum sequence length must be less than maximum');
        }

        if (min < 512) {
            errors.push('Minimum sequence length must be at least 512 tokens');
        }

        if (max > 32768) {
            errors.push('Maximum sequence length cannot exceed 32768 tokens');
        }

        if (min > 32768) {
            errors.push('Minimum sequence length cannot exceed 32768 tokens');
        }

        if (max < 512) {
            errors.push('Maximum sequence length must be at least 512 tokens');
        }

        // Performance and usability warnings
        if (errors.length === 0) {
            const dataPoints = Math.ceil((max - min) / 1024);

            if (dataPoints > 50) {
                warnings.push(`Large range will generate ${dataPoints} data points - may impact performance`);
            }

            if (dataPoints < 3) {
                warnings.push('Small range may not provide meaningful visualization');
            }

            if (max - min < 1024) {
                warnings.push('Range is smaller than default step size (1024) - consider adjusting');
            }

            // Helpful information
            info.push(`Will generate approximately ${dataPoints} data points`);

            if (min >= 8192 || max >= 16384) {
                info.push('Long sequences may require significant memory');
            }

            // Memory estimation
            const estimatedMemoryGB = this.estimateSequenceLengthMemoryUsage(min, max, dataPoints);
            if (estimatedMemoryGB > 1) {
                info.push(`Estimated memory usage: ~${estimatedMemoryGB.toFixed(1)}GB`);
            }
        }

        // Display validation results with enhanced styling
        if (errors.length > 0) {
            validationDiv.innerHTML = `
                <div class="alert alert-danger py-2 mb-0">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    <strong>Validation Error:</strong>
                    <ul class="mb-0 mt-1">
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-lightbulb me-1"></i>
                            Tip: Valid range is 512-32768 tokens with minimum less than maximum
                        </small>
                    </div>
                </div>
            `;
            this.setSequenceLengthInputValidationState('invalid');
            return false;
        } else if (warnings.length > 0) {
            validationDiv.innerHTML = `
                <div class="alert alert-warning py-2 mb-0">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    <strong>Warning:</strong>
                    <ul class="mb-0 mt-1">
                        ${warnings.map(warning => `<li>${warning}</li>`).join('')}
                    </ul>
                    ${info.length > 0 ? `<div class="mt-1 text-muted small">${info.join(', ')}</div>` : ''}
                </div>
            `;
            this.setSequenceLengthInputValidationState('warning');
        } else {
            validationDiv.innerHTML = `
                <div class="alert alert-success py-2 mb-0">
                    <i class="fas fa-check-circle me-1"></i>
                    <strong>Valid range:</strong> ${min} - ${max} tokens
                    <div class="text-muted small mt-1">${info.join(', ')}</div>
                </div>
            `;
            this.setSequenceLengthInputValidationState('valid');
        }

        return errors.length === 0;
    }

    /**
     * Estimate memory usage for sequence length analysis
     */
    estimateSequenceLengthMemoryUsage(min, max, dataPoints) {
        const avgSequenceLength = (min + max) / 2;
        const bytesPerToken = this.getDtypeBytes(this.memoryState.kvCacheDtype);
        const estimatedBytesPerPoint = avgSequenceLength * bytesPerToken * 1024; // Rough multiplier
        return (estimatedBytesPerPoint * dataPoints) / (1024 * 1024 * 1024); // Convert to GB
    }

    /**
     * Get bytes per element for dtype
     */
    getDtypeBytes(dtype) {
        const dtypeBytes = {
            'fp32': 4,
            'fp16': 2,
            'bf16': 2,
            'int8': 1,
            'int4': 0.5
        };
        return dtypeBytes[dtype] || 2; // Default to fp16
    }

    /**
     * Set validation state for sequence length inputs
     */
    setSequenceLengthInputValidationState(state) {
        const inputs = [
            document.getElementById('minSequenceLength'),
            document.getElementById('maxSequenceLength')
        ];

        inputs.forEach(input => {
            if (!input) return;

            // Remove existing validation classes
            input.classList.remove('is-valid', 'is-invalid');

            // Add appropriate class based on state
            if (state === 'valid') {
                input.classList.add('is-valid');
            } else if (state === 'invalid') {
                input.classList.add('is-invalid');
            }
            // 'warning' state doesn't add Bootstrap validation classes
        });
    }

    /**
     * Show KV memory growth chart
     */
    async showKvGrowthChart() {
        const selectedModels = $('#modelSelect').val() || [];

        if (selectedModels.length === 0) {
            this.showError('Please select at least one model to analyze KV memory growth.');
            return;
        }

        if (!this.validateSequenceLengthRange()) {
            this.showError('Please fix sequence length validation errors before proceeding.');
            return;
        }

        try {
            // Show loading state
            const btn = document.getElementById('showKvGrowthBtn');
            const originalText = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';

            // Prepare request data
            const requestData = {
                model_names: selectedModels,
                min_sequence_length: this.memoryState.minSequenceLength,
                max_sequence_length: this.memoryState.maxSequenceLength,
                sequence_length_step: 1024, // Fixed step size for now
                batch_size: parseInt(document.getElementById('batchSize')?.value) || 1,
                dtype: this.memoryState.kvCacheDtype
            };

            // Make API request
            const response = await fetch(`${this.apiBaseUrl}/memory/kv-growth`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            // Display the chart
            this.displayKvGrowthChart(data);

            this.showToast('KV memory growth chart generated successfully', 'success');

        } catch (error) {
            console.error('Failed to generate KV growth chart:', error);
            this.handleKvGrowthApiError(error);
        } finally {
            // Restore button state
            const btn = document.getElementById('showKvGrowthBtn');
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }
    }

    /**
     * Display KV memory growth chart
     */
    displayKvGrowthChart(data) {
        // Find or create chart container
        let chartContainer = document.getElementById('kvGrowthChartContainer');
        if (!chartContainer) {
            chartContainer = document.createElement('div');
            chartContainer.id = 'kvGrowthChartContainer';
            chartContainer.className = 'card mb-4';
            chartContainer.innerHTML = `
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        KV Cache Memory Growth Analysis
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 250px; position: relative;">
                        <canvas id="kvGrowthChart"></canvas>
                    </div>
                    <div id="kvGrowthLegend" class="mt-2"></div>
                </div>
            `;

            // Insert into results area
            const resultsContainer = document.getElementById('analysisResults');
            if (resultsContainer) {
                resultsContainer.appendChild(chartContainer);
                resultsContainer.style.display = 'block';
            }
        }

        // Prepare chart data
        const chartData = this.prepareKvChartData(data);

        // Create or update chart
        this.createKvGrowthChart(chartData);

        // Update legend
        this.updateKvGrowthLegend(data);
    }

    /**
     * Prepare data for KV growth Chart.js
     */
    prepareKvChartData(data) {
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
        ];

        const datasets = [];
        let colorIndex = 0;

        Object.entries(data.kv_growth_data || {}).forEach(([modelName, growthPoints]) => {
            const color = colors[colorIndex % colors.length];
            const shortName = modelName.split('/').pop();

            datasets.push({
                label: shortName,
                data: growthPoints.map(point => ({
                    x: point.sequence_length,
                    y: point.memory_bytes / (1024 * 1024 * 1024) // Convert to GB
                })),
                borderColor: color,
                backgroundColor: color + '20',
                fill: false,
                tension: 0.1,
                pointRadius: 4,
                pointHoverRadius: 6
            });

            colorIndex++;
        });

        return { datasets: datasets };
    }

    /**
     * Create KV growth chart using Chart.js
     */
    createKvGrowthChart(chartData) {
        const canvas = document.getElementById('kvGrowthChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // Destroy existing chart if it exists
        if (this.kvGrowthChart) {
            this.kvGrowthChart.destroy();
        }

        this.kvGrowthChart = new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        top: 5,
                        right: 5,
                        bottom: 5,
                        left: 5
                    }
                },
                scales: {
                    x: {
                        type: 'linear',
                        position: 'bottom',
                        title: {
                            display: true,
                            text: 'Sequence Length (tokens)',
                            font: {
                                size: 10,
                                weight: 'normal'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            font: {
                                size: 9
                            }
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'KV Cache Memory (GB)',
                            font: {
                                size: 10,
                                weight: 'normal'
                            }
                        },
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            font: {
                                size: 9
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        borderWidth: 1,
                        titleFont: {
                            size: 11
                        },
                        bodyFont: {
                            size: 10
                        },
                        callbacks: {
                            title: (context) => {
                                return `Sequence Length: ${context[0].parsed.x} tokens`;
                            },
                            label: (context) => {
                                const value = context.parsed.y.toFixed(2);
                                return `${context.dataset.label}: ${value} GB`;
                            }
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 8,
                            font: {
                                size: 9
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                elements: {
                    line: {
                        borderWidth: 2
                    },
                    point: {
                        radius: 3,
                        hoverRadius: 5
                    }
                }
            }
        });
    }

    /**
     * Update KV growth chart legend with attention mechanism info
     */
    updateKvGrowthLegend(data) {
        const legendDiv = document.getElementById('kvGrowthLegend');
        if (!legendDiv) return;

        const modelResults = data.model_results || {};
        const legendItems = [];

        Object.entries(modelResults).forEach(([modelName, result]) => {
            const shortName = modelName.split('/').pop();
            const attentionType = result.attention_mechanism || 'Unknown';
            legendItems.push(`
                <span class="badge bg-secondary me-2">${shortName}</span>
                <small class="text-muted">${attentionType}</small>
            `);
        });

        legendDiv.innerHTML = `
            <div class="d-flex flex-wrap gap-1 align-items-center" style="font-size: 0.85rem;">
                <strong class="me-1" style="font-size: 0.8rem;">Attention:</strong>
                ${legendItems.join('')}
            </div>
            <div class="mt-1">
                <small class="text-muted" style="font-size: 0.75rem;">
                    <i class="fas fa-info-circle me-1"></i>
                    MHA: Multi-Head, GQA: Grouped Query, MLA: Multi-Layer
                </small>
            </div>
        `;
    }

    /**
     * Handle memory controls state changes
     */
    onMemoryControlsChange(property, value, state) {
        console.log('Memory controls changed:', property, value);

        // Handle different property changes
        switch (property) {
            case 'showTotalMemory':
                this.handleMemoryToggleChange(value);
                break;
            case 'kvCacheDtype':
                this.handleKvDtypeChange(value);
                break;
            case 'sequenceLength':
                this.handleSequenceLengthRangeChange(value);
                break;
        }
    }

    /**
     * Handle memory toggle changes
     */
    handleMemoryToggleChange(showMemory) {
        // Update memory state
        this.memoryState.showTotalMemory = showMemory;

        // Update UI to show/hide memory-related information
        const memoryElements = document.querySelectorAll('.memory-info, .total-memory');
        memoryElements.forEach(element => {
            element.style.display = showMemory ? 'block' : 'none';
        });

        // Update comparison table headers if they exist
        this.updateComparisonTableMemoryColumns(showMemory);

        // Update existing model cards if they exist
        this.updateExistingModelCardsMemoryDisplay(showMemory);

        // If memory is enabled and we have results, refresh calculations
        if (showMemory && this.currentAnalysisId) {
            this.refreshMemoryCalculations();
        }

        // Preserve other user selections - ensure form values remain unchanged
        this.preserveUserSelections();

        // Update analysis request to include memory calculations
        this.updateAnalysisRequestForMemory(showMemory);

        // Emit event for other components
        const event = new CustomEvent('memoryToggleChange', {
            detail: { showMemory: showMemory }
        });
        document.dispatchEvent(event);

        // Show user feedback
        const message = showMemory ? 'Memory analysis enabled' : 'Memory analysis disabled';
        this.showToast(message, 'info');
    }

    /**
     * Handle hardware selection changes
     */
    onHardwareSelectionChange(hardware) {
        console.log('Hardware selection changed:', hardware);
        
        // Store selected hardware
        this.selectedHardware = hardware;
        
        // Update analysis configuration to include hardware
        this.updateAnalysisConfigurationForHardware(hardware);
        
        // Update roofline visualizer with selected hardware
        if (this.rooflineVisualizer) {
            if (hardware) {
                this.rooflineVisualizer.setHardware([hardware]);
                this.showRooflineVisualization();
            } else {
                this.hideRooflineVisualization();
            }
        }
        
        // Show user feedback
        if (hardware) {
            this.showToast(`Hardware selected: ${hardware.name}`, 'success');
        } else {
            this.showToast('Hardware selection cleared', 'info');
        }
    }

    /**
     * Handle hardware selection change events
     */
    handleHardwareSelectionChange(detail) {
        const { hardware, hardwareId } = detail;
        
        // Update UI elements that depend on hardware selection
        this.updateHardwareDependentUI(hardware);
        
        // Validate current configuration against selected hardware
        if (hardware) {
            this.validateConfigurationAgainstHardware(hardware);
        }
    }

    /**
     * Update analysis configuration for hardware
     */
    updateAnalysisConfigurationForHardware(hardware) {
        if (!hardware) return;
        
        // This method can be extended to modify analysis parameters
        // based on selected hardware capabilities
        console.log('Updating analysis configuration for hardware:', hardware.id);
    }

    /**
     * Update UI elements that depend on hardware selection
     */
    updateHardwareDependentUI(hardware) {
        // Update precision options based on hardware capabilities
        if (hardware && hardware.supported_precisions) {
            this.updatePrecisionOptionsForHardware(hardware.supported_precisions);
        }
        
        // Update memory constraints based on hardware memory
        if (hardware && hardware.memory_size_gb) {
            this.updateMemoryConstraintsForHardware(hardware.memory_size_gb);
        }
    }

    /**
     * Update precision options based on hardware capabilities
     */
    updatePrecisionOptionsForHardware(supportedPrecisions) {
        const precisionSelects = [
            document.getElementById('precision'),
            document.getElementById('weightDtype'),
            document.getElementById('activationDtype'),
            document.getElementById('gradDtype'),
            document.getElementById('optimizerDtype'),
            document.getElementById('kvCacheDtype')
        ];

        precisionSelects.forEach(select => {
            if (!select) return;

            // Store current value
            const currentValue = select.value;
            
            // Update options based on hardware support
            Array.from(select.options).forEach(option => {
                const precision = option.value;
                const isSupported = supportedPrecisions.includes(precision);
                
                // Disable unsupported precisions
                option.disabled = !isSupported;
                
                // Add visual indication
                if (!isSupported) {
                    option.textContent = option.textContent.replace(' (unsupported)', '') + ' (unsupported)';
                } else {
                    option.textContent = option.textContent.replace(' (unsupported)', '');
                }
            });

            // If current value is not supported, select a supported one
            if (!supportedPrecisions.includes(currentValue) && supportedPrecisions.length > 0) {
                // Prefer fp16 if available, otherwise use first supported precision
                const preferredPrecision = supportedPrecisions.includes('fp16') ? 'fp16' : supportedPrecisions[0];
                select.value = preferredPrecision;
                
                // Trigger change event
                select.dispatchEvent(new Event('change'));
            }
        });
    }

    /**
     * Update memory constraints based on hardware memory
     */
    updateMemoryConstraintsForHardware(memorySize) {
        // This could be used to show memory usage warnings
        // or adjust batch size recommendations
        console.log(`Hardware memory: ${memorySize}GB`);
    }

    /**
     * Validate current configuration against selected hardware
     */
    validateConfigurationAgainstHardware(hardware) {
        const validationResults = [];
        
        // Check memory requirements
        const batchSize = parseInt(document.getElementById('batchSize')?.value) || 1;
        const sequenceLength = parseInt(document.getElementById('sequenceLength')?.value) || 2048;
        
        // Rough memory estimation (this could be more sophisticated)
        const estimatedMemoryGB = (batchSize * sequenceLength * 0.001); // Very rough estimate
        
        if (estimatedMemoryGB > hardware.memory_size_gb * 0.8) {
            validationResults.push({
                type: 'warning',
                message: `Current configuration may exceed hardware memory capacity (${hardware.memory_size_gb}GB)`
            });
        }
        
        // Check precision support
        const currentPrecision = document.getElementById('precision')?.value;
        if (currentPrecision && !hardware.supported_precisions.includes(currentPrecision)) {
            validationResults.push({
                type: 'error',
                message: `Selected precision (${currentPrecision}) is not supported by this hardware`
            });
        }
        
        // Show validation results
        this.displayHardwareValidationResults(validationResults);
    }

    /**
     * Display hardware validation results
     */
    displayHardwareValidationResults(results) {
        if (results.length === 0) return;
        
        // Show validation messages
        results.forEach(result => {
            this.showToast(result.message, result.type);
        });
    }

    /**
     * Show roofline visualization
     */
    showRooflineVisualization() {
        const rooflineContainer = document.getElementById('rooflineVisualizationContainer');
        if (rooflineContainer) {
            rooflineContainer.style.display = 'block';
            
            // Show the analysis results container if hidden
            const analysisResults = document.getElementById('analysisResults');
            if (analysisResults) {
                analysisResults.style.display = 'block';
            }
            
            // Hide welcome message
            const welcomeMessage = document.getElementById('welcomeMessage');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'none';
            }
        }
    }

    /**
     * Hide roofline visualization
     */
    hideRooflineVisualization() {
        const rooflineContainer = document.getElementById('rooflineVisualizationContainer');
        if (rooflineContainer) {
            rooflineContainer.style.display = 'none';
        }
    }

    /**
     * Show interactive controls
     */
    showInteractiveControls() {
        const controlsContainer = document.getElementById('interactiveControlsContainer');
        if (controlsContainer) {
            controlsContainer.style.display = 'block';
            
            // Show the analysis results container if hidden
            const analysisResults = document.getElementById('analysisResults');
            if (analysisResults) {
                analysisResults.style.display = 'block';
            }
            
            // Hide welcome message
            const welcomeMessage = document.getElementById('welcomeMessage');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'none';
            }
        }
    }

    /**
     * Hide interactive controls
     */
    hideInteractiveControls() {
        const controlsContainer = document.getElementById('interactiveControlsContainer');
        if (controlsContainer) {
            controlsContainer.style.display = 'none';
        }
    }

    /**
     * Show advanced visualization controls
     */
    showAdvancedVisualizationControls() {
        const controlsContainer = document.getElementById('advancedVisualizationControlsContainer');
        if (controlsContainer) {
            controlsContainer.style.display = 'block';
            
            // Show the analysis results container if hidden
            const analysisResults = document.getElementById('analysisResults');
            if (analysisResults) {
                analysisResults.style.display = 'block';
            }
            
            // Hide welcome message
            const welcomeMessage = document.getElementById('welcomeMessage');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'none';
            }
        }
    }

    /**
     * Hide advanced visualization controls
     */
    hideAdvancedVisualizationControls() {
        const controlsContainer = document.getElementById('advancedVisualizationControlsContainer');
        if (controlsContainer) {
            controlsContainer.style.display = 'none';
        }
    }

    /**
     * Handle roofline operator click events
     */
    onRooflineOperatorClick(dataset, index) {
        console.log('Roofline operator clicked:', dataset, index);
        
        if (dataset.operatorData && dataset.operatorData[index]) {
            const operator = dataset.operatorData[index];
            this.showOperatorDetails(operator);
        }
    }

    /**
     * Handle roofline precision change events
     */
    onRooflinePrecisionChange(precisions) {
        console.log('Roofline precision changed:', precisions);
        
        // Update main precision selector to match if single precision selected
        if (precisions.length === 1) {
            const precisionSelect = document.getElementById('precision');
            if (precisionSelect && precisionSelect.value !== precisions[0]) {
                precisionSelect.value = precisions[0];
                precisionSelect.dispatchEvent(new Event('change'));
            }
        }
    }

    /**
     * Handle interactive controls parameter change events
     */
    onInteractiveParameterChange(parameters) {
        console.log('Interactive parameters changed:', parameters);
        
        // Update main dashboard controls to match
        this.syncParametersWithMainControls(parameters);
        
        // Trigger analysis update if models are selected
        if (this.hasSelectedModels()) {
            this.scheduleAnalysisUpdate();
        }
    }

    /**
     * Handle interactive controls precision change events
     */
    onInteractivePrecisionChange(precisionConfig) {
        console.log('Interactive precision changed:', precisionConfig);
        
        // Update main precision controls to match
        this.syncPrecisionWithMainControls(precisionConfig);
        
        // Update roofline visualization if active
        if (this.rooflineVisualizer) {
            this.rooflineVisualizer.updatePrecisionSettings(precisionConfig);
        }
    }

    /**
     * Handle interactive controls preset load events
     */
    onInteractivePresetLoad(presetKey, preset) {
        console.log('Interactive preset loaded:', presetKey, preset);
        
        // Apply preset to main dashboard
        this.applyPresetToMainDashboard(preset);
        
        // Show success message
        this.showToast(`Applied preset: ${preset.name}`, 'success');
    }

    /**
     * Handle visualization filter change events
     */
    onVisualizationFilterChange(filters) {
        console.log('Visualization filters changed:', filters);
        
        // Apply filters to roofline visualization
        if (this.rooflineVisualizer) {
            this.rooflineVisualizer.applyFilters(filters);
        }
        
        // Apply filters to timing dashboard
        if (this.timingDashboard) {
            this.timingDashboard.applyFilters(filters);
        }
    }

    /**
     * Handle visualization grouping change events
     */
    onVisualizationGroupingChange(grouping) {
        console.log('Visualization grouping changed:', grouping);
        
        // Apply grouping to visualizations
        if (this.rooflineVisualizer) {
            this.rooflineVisualizer.applyGrouping(grouping);
        }
    }

    /**
     * Handle visualization export events
     */
    async onVisualizationExport(type, format) {
        console.log('Visualization export requested:', type, format);
        
        try {
            switch (type) {
                case 'chart':
                    return await this.exportChart(format);
                case 'data':
                    return await this.exportData(format);
                case 'config':
                    return await this.exportConfig(format);
                default:
                    throw new Error(`Unknown export type: ${type}`);
            }
        } catch (error) {
            console.error('Export failed:', error);
            throw error;
        }
    }

    /**
     * Handle visualization share events
     */
    onVisualizationShare(shareUrl, sessionData) {
        console.log('Visualization shared:', shareUrl);
        
        // Could integrate with social sharing APIs or analytics
        this.showToast('Share link created and copied to clipboard', 'success');
    }

    /**
     * Handle visualization zoom reset events
     */
    onVisualizationZoomReset() {
        if (this.rooflineVisualizer && this.rooflineVisualizer.chart) {
            this.rooflineVisualizer.resetZoom();
        }
    }

    /**
     * Handle visualization zoom to fit events
     */
    onVisualizationZoomToFit() {
        if (this.rooflineVisualizer && this.rooflineVisualizer.chart) {
            this.rooflineVisualizer.zoomToFit();
        }
    }

    /**
     * Handle visualization persist labels change events
     */
    onVisualizationPersistLabelsChange(persist) {
        if (this.rooflineVisualizer) {
            this.rooflineVisualizer.setPersistLabels(persist);
        }
    }

    /**
     * Show operator details in a modal or panel
     */
    showOperatorDetails(operator) {
        // This could show detailed operator information
        const details = [
            `Operator: ${operator.name}`,
            `Operational Intensity: ${operator.operational_intensity.toFixed(3)} FLOP/Byte`,
            `Performance: ${operator.achieved_performance_tflops.toFixed(2)} TFLOPS`,
            `Utilization: ${operator.utilization_percent || 'N/A'}%`,
            `Bottleneck: ${operator.bottleneck_type || 'Unknown'}`
        ].join('\n');
        
        this.showToast(`Operator Details:\n${details}`, 'info', 8000);
    }

    /**
     * Update roofline visualizer with analysis results
     */
    updateRooflineWithAnalysisResults(results) {
        if (!this.rooflineVisualizer || !this.selectedHardware) return;
        
        // Extract operator data from analysis results
        const operatorData = this.extractOperatorDataFromResults(results);
        
        if (operatorData.length > 0) {
            this.rooflineVisualizer.setOperators(operatorData);
            this.showRooflineVisualization();
        }
    }

    /**
     * Extract operator data from analysis results for roofline plotting
     */
    extractOperatorDataFromResults(results) {
        const operatorData = [];
        
        // This would extract operator timing and performance data
        // For now, return mock data structure
        Object.entries(results.results || {}).forEach(([modelName, modelResult]) => {
            // Mock operator data - in real implementation, this would come from
            // operator timing analysis results
            const mockOperators = [
                {
                    name: `${modelName}_attention`,
                    operational_intensity: Math.random() * 10 + 0.1,
                    achieved_performance_tflops: Math.random() * 100 + 10,
                    utilization_percent: Math.random() * 80 + 20,
                    bottleneck_type: Math.random() > 0.5 ? 'compute' : 'memory'
                },
                {
                    name: `${modelName}_mlp`,
                    operational_intensity: Math.random() * 50 + 1,
                    achieved_performance_tflops: Math.random() * 150 + 20,
                    utilization_percent: Math.random() * 90 + 10,
                    bottleneck_type: Math.random() > 0.3 ? 'compute' : 'memory'
                }
            ];
            
            operatorData.push(...mockOperators);
        });
        
        return operatorData;
    }

    /**
     * Update timing dashboard with analysis results
     */
    updateTimingDashboardWithResults(results) {
        if (!this.selectedHardware) return;
        
        // Extract operator data from analysis results
        const operatorData = this.extractOperatorDataFromResults(results);
        
        if (operatorData.length > 0 && window.timingDashboard) {
            // Convert operator data to timing analysis format
            const operators = operatorData.map(op => ({
                name: op.name,
                type: this.inferOperatorType(op.name),
                parameters: this.extractOperatorParameters(results, op.name)
            }));
            
            // Analyze timing with the selected hardware
            window.timingDashboard.analyzeOperatorTiming(operators, this.selectedHardware)
                .then(() => {
                    this.showTimingDashboard();
                    
                    // Generate optimization suggestions based on timing data
                    if (window.optimizationSuggestions && window.timingDashboard.timingData) {
                        window.optimizationSuggestions.generateOptimizationSuggestions(
                            window.timingDashboard.timingData, 
                            this.selectedHardware
                        ).then(() => {
                            this.showOptimizationSuggestions();
                        });
                    }
                })
                .catch(error => {
                    console.error('Failed to analyze timing:', error);
                });
        }
    }

    /**
     * Sync interactive control parameters with main dashboard controls
     */
    syncParametersWithMainControls(parameters) {
        // Update batch size
        const batchSizeInput = document.getElementById('batchSize');
        if (batchSizeInput && parameters.batchSize !== undefined) {
            batchSizeInput.value = parameters.batchSize;
        }
        
        // Update sequence length
        const sequenceLengthInput = document.getElementById('sequenceLength');
        if (sequenceLengthInput && parameters.sequenceLength !== undefined) {
            sequenceLengthInput.value = parameters.sequenceLength;
        }
        
        // Update precision if not using mixed precision
        if (!parameters.mixedPrecision && parameters.precision) {
            const precisionSelect = document.getElementById('precision');
            if (precisionSelect) {
                precisionSelect.value = parameters.precision;
            }
        }
    }

    /**
     * Sync interactive control precision settings with main dashboard
     */
    syncPrecisionWithMainControls(precisionConfig) {
        if (precisionConfig.enabled) {
            // Enable mixed precision in main controls
            const mixedPrecisionToggle = document.getElementById('enableMixedPrecision');
            if (mixedPrecisionToggle && !mixedPrecisionToggle.checked) {
                mixedPrecisionToggle.checked = true;
                mixedPrecisionToggle.dispatchEvent(new Event('change'));
            }
            
            // Update mixed precision selectors
            const selectors = {
                'weightDtype': precisionConfig.weightDtype,
                'activationDtype': precisionConfig.activationDtype,
                'gradDtype': precisionConfig.gradDtype,
                'optimizerDtype': precisionConfig.optimizerDtype
            };
            
            Object.entries(selectors).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element && value) {
                    element.value = value;
                }
            });
        } else {
            // Use simple precision
            const precisionSelect = document.getElementById('precision');
            if (precisionSelect && precisionConfig.precision) {
                precisionSelect.value = precisionConfig.precision;
            }
        }
    }

    /**
     * Apply preset configuration to main dashboard
     */
    applyPresetToMainDashboard(preset) {
        const config = preset.config;
        
        // Apply batch size and sequence length
        this.syncParametersWithMainControls(config);
        
        // Apply precision settings
        if (config.mixedPrecision && config.mixedPrecisionConfig) {
            this.syncPrecisionWithMainControls({
                enabled: true,
                ...config.mixedPrecisionConfig
            });
        } else {
            this.syncPrecisionWithMainControls({
                enabled: false,
                precision: config.precision
            });
        }
    }

    /**
     * Check if any models are currently selected
     */
    hasSelectedModels() {
        const modelSelect = document.getElementById('modelSelect');
        return modelSelect && modelSelect.selectedOptions.length > 0;
    }

    /**
     * Schedule an analysis update with debouncing
     */
    scheduleAnalysisUpdate() {
        if (this.analysisUpdateTimer) {
            clearTimeout(this.analysisUpdateTimer);
        }
        
        this.analysisUpdateTimer = setTimeout(() => {
            if (this.hasSelectedModels()) {
                console.log('Auto-updating analysis due to parameter changes');
                // Could trigger automatic re-analysis here
                // For now, just show a notification
                this.showToast('Parameters updated. Click "Analyze Models" to see updated results.', 'info');
            }
        }, 1000);
    }

    /**
     * Export chart in specified format
     */
    async exportChart(format) {
        if (this.rooflineVisualizer && this.rooflineVisualizer.chart) {
            return this.rooflineVisualizer.exportChart(format);
        }
        throw new Error('No chart available for export');
    }

    /**
     * Export data in specified format
     */
    async exportData(format) {
        // Collect current analysis data
        const data = {
            models: this.getSelectedModels(),
            parameters: this.getCurrentParameters(),
            hardware: this.selectedHardware,
            results: this.currentAnalysisResults,
            timestamp: new Date().toISOString()
        };
        
        let content, mimeType, extension;
        
        switch (format) {
            case 'json':
                content = JSON.stringify(data, null, 2);
                mimeType = 'application/json';
                extension = 'json';
                break;
            case 'csv':
                content = this.convertToCSV(data);
                mimeType = 'text/csv';
                extension = 'csv';
                break;
            case 'xlsx':
                // Would need a library like SheetJS for Excel export
                throw new Error('Excel export not implemented yet');
            default:
                throw new Error(`Unsupported data format: ${format}`);
        }
        
        // Create and download file
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `llm-analysis-data.${extension}`;
        link.click();
        URL.revokeObjectURL(url);
    }

    /**
     * Export configuration in specified format
     */
    async exportConfig(format) {
        const config = {
            models: this.getSelectedModels(),
            parameters: this.getCurrentParameters(),
            hardware: this.selectedHardware ? this.selectedHardware.id : null,
            interactiveControls: this.interactiveControls ? this.interactiveControls.getCurrentParameters() : null,
            visualizationControls: this.advancedVisualizationControls ? {
                filters: this.advancedVisualizationControls.getCurrentFilters(),
                grouping: this.advancedVisualizationControls.getCurrentGrouping()
            } : null,
            timestamp: new Date().toISOString()
        };
        
        let content, mimeType, extension;
        
        switch (format) {
            case 'json':
                content = JSON.stringify(config, null, 2);
                mimeType = 'application/json';
                extension = 'json';
                break;
            case 'yaml':
                // Simple YAML conversion (in production, use a proper YAML library)
                content = this.convertToYAML(config);
                mimeType = 'text/yaml';
                extension = 'yaml';
                break;
            default:
                throw new Error(`Unsupported config format: ${format}`);
        }
        
        // Create and download file
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `llm-analysis-config.${extension}`;
        link.click();
        URL.revokeObjectURL(url);
    }

    /**
     * Convert data to CSV format
     */
    convertToCSV(data) {
        // Simple CSV conversion - in production, use a proper CSV library
        const rows = [];
        rows.push(['Model', 'Parameter', 'Value']);
        
        if (data.models) {
            data.models.forEach(model => {
                rows.push([model, 'Selected', 'true']);
            });
        }
        
        if (data.parameters) {
            Object.entries(data.parameters).forEach(([key, value]) => {
                rows.push(['Configuration', key, String(value)]);
            });
        }
        
        return rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
    }

    /**
     * Convert object to YAML format (simple implementation)
     */
    convertToYAML(obj, indent = 0) {
        const spaces = '  '.repeat(indent);
        let yaml = '';
        
        Object.entries(obj).forEach(([key, value]) => {
            if (value === null || value === undefined) {
                yaml += `${spaces}${key}: null\n`;
            } else if (typeof value === 'object' && !Array.isArray(value)) {
                yaml += `${spaces}${key}:\n`;
                yaml += this.convertToYAML(value, indent + 1);
            } else if (Array.isArray(value)) {
                yaml += `${spaces}${key}:\n`;
                value.forEach(item => {
                    yaml += `${spaces}  - ${item}\n`;
                });
            } else {
                yaml += `${spaces}${key}: ${value}\n`;
            }
        });
        
        return yaml;
    }

    /**
     * Get currently selected models
     */
    getSelectedModels() {
        const modelSelect = document.getElementById('modelSelect');
        return modelSelect ? Array.from(modelSelect.selectedOptions).map(opt => opt.value) : [];
    }

    /**
     * Get current analysis parameters
     */
    getCurrentParameters() {
        return {
            batchSize: parseInt(document.getElementById('batchSize')?.value || '1'),
            sequenceLength: parseInt(document.getElementById('sequenceLength')?.value || '2048'),
            precision: document.getElementById('precision')?.value || 'fp16',
            trainingMode: document.getElementById('trainingMode')?.checked || false,
            enableParallel: document.getElementById('enableParallel')?.checked || false
        };
    }

    /**
     * Infer operator type from operator name
     */
    inferOperatorType(operatorName) {
        const name = operatorName.toLowerCase();
        if (name.includes('attention')) return 'attention';
        if (name.includes('mlp') || name.includes('feedforward')) return 'mlp';
        if (name.includes('embedding')) return 'embedding';
        if (name.includes('norm')) return 'normalization';
        if (name.includes('moe') || name.includes('expert')) return 'moe';
        return 'generic';
    }

    /**
     * Extract operator parameters from analysis results
     */
    extractOperatorParameters(results, operatorName) {
        // Extract relevant parameters from the analysis results
        // This is a simplified implementation
        const firstModel = Object.values(results.results || {})[0];
        if (!firstModel) return {};
        
        return {
            batch_size: firstModel.batch_size || 1,
            sequence_length: firstModel.sequence_length || 2048,
            hidden_size: firstModel.config?.hidden_size || 4096,
            num_heads: firstModel.config?.num_attention_heads || 32
        };
    }

    /**
     * Show timing dashboard
     */
    showTimingDashboard() {
        if (window.timingDashboard) {
            window.timingDashboard.show();
        }
    }

    /**
     * Show optimization suggestions
     */
    showOptimizationSuggestions() {
        if (window.optimizationSuggestions) {
            window.optimizationSuggestions.show();
        }
    }

    /**
     * Update comparison table to show/hide memory columns
     */
    updateComparisonTableMemoryColumns(showMemory) {
        const comparisonTable = document.querySelector('#comparisonResults table');
        if (!comparisonTable) return;

        // Find memory column header and cells
        const headers = comparisonTable.querySelectorAll('th');
        const rows = comparisonTable.querySelectorAll('tbody tr');

        headers.forEach((header, index) => {
            if (header.textContent.toLowerCase().includes('memory')) {
                header.style.display = showMemory ? '' : 'none';

                // Hide corresponding cells in all rows
                rows.forEach(row => {
                    const cell = row.cells[index];
                    if (cell) {
                        cell.style.display = showMemory ? '' : 'none';
                    }
                });
            }
        });
    }

    /**
     * Update existing model cards to show/hide memory information
     */
    updateExistingModelCardsMemoryDisplay(showMemory) {
        const modelCards = document.querySelectorAll('[data-model]');
        modelCards.forEach(card => {
            // Update total memory column
            const totalMemoryCol = card.querySelector('.total-memory');
            if (totalMemoryCol) {
                totalMemoryCol.style.display = showMemory ? 'block' : 'none';
            }

            // Update memory info section
            const memoryInfo = card.querySelector('.memory-info');
            if (memoryInfo) {
                memoryInfo.style.display = showMemory ? 'block' : 'none';
            }
        });
    }

    /**
     * Preserve user selections when memory controls change
     */
    preserveUserSelections() {
        // Ensure model selection is preserved
        const currentModels = $('#modelSelect').val() || [];

        // Ensure other form values are preserved
        const formElements = [
            'sequenceLength', 'batchSize', 'precision',
            'includeShapes', 'includeComparison', 'enableParallel'
        ];

        const preservedValues = {};
        formElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    preservedValues[id] = element.checked;
                } else {
                    preservedValues[id] = element.value;
                }
            }
        });

        // Also preserve parallel configuration values
        const parallelElements = ['tensorParallel', 'pipelineParallel', 'dataParallel', 'expertParallel'];
        parallelElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                preservedValues[id] = element.value;
            }
        });

        // Store preserved state for potential restoration
        this.preservedUserState = {
            models: currentModels,
            formValues: preservedValues,
            memoryState: { ...this.memoryState },
            timestamp: Date.now()
        };

        console.log('User selections preserved:', this.preservedUserState);
    }

    /**
     * Restore user selections if needed
     */
    restoreUserSelections() {
        if (!this.preservedUserState) return;

        const { models, formValues, memoryState } = this.preservedUserState;

        // Restore model selection
        if (models && models.length > 0) {
            $('#modelSelect').val(models).trigger('change');
        }

        // Restore form values
        Object.entries(formValues).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value;
                } else {
                    element.value = value;
                }
            }
        });

        // Restore memory state
        if (memoryState) {
            this.memoryState = { ...this.memoryState, ...memoryState };
        }

        console.log('User selections restored');
    }

    /**
     * Handle KV cache dtype changes
     */
    handleKvDtypeChange(dtype) {
        // Update memory state
        this.memoryState.kvCacheDtype = dtype;

        // Preserve other user selections
        this.preserveUserSelections();

        // If we have current results, recalculate with new dtype
        if (this.currentAnalysisId && this.memoryState.showTotalMemory) {
            this.refreshMemoryCalculations();
        }

        // Update existing model cards with new dtype calculations
        this.updateModelCardsWithNewDtype(dtype);

        // Show user feedback
        this.showToast(`KV cache data type changed to ${dtype.toUpperCase()}`, 'info');

        // Emit event for other components
        const event = new CustomEvent('kvDtypeChange', {
            detail: { dtype: dtype }
        });
        document.dispatchEvent(event);
    }

    /**
     * Handle sequence length range changes
     */
    handleSequenceLengthRangeChange(rangeConfig) {
        console.log('Sequence length range changed:', rangeConfig);

        // Update the main sequence length input to match the minimum of the range
        // This ensures consistency between the main analysis and KV growth analysis
        const mainSequenceLengthInput = document.getElementById('sequenceLength');
        if (mainSequenceLengthInput && rangeConfig.min) {
            // Only update if the current value is outside the new range
            const currentValue = parseInt(mainSequenceLengthInput.value);
            if (currentValue < rangeConfig.min || currentValue > rangeConfig.max) {
                mainSequenceLengthInput.value = rangeConfig.min;

                // Show user feedback about the change
                this.showToast(
                    `Main sequence length updated to ${rangeConfig.min} to match KV analysis range`,
                    'info'
                );
            }
        }

        // If we have current results and memory is enabled, suggest refreshing
        if (this.currentAnalysisId && this.memoryState?.showTotalMemory) {
            this.showToast(
                'Sequence length range updated. Consider re-running analysis for updated results.',
                'info'
            );
        }
    }

    /**
     * Refresh memory calculations with current settings
     */
    async refreshMemoryCalculations() {
        if (!this.memoryControls) return;

        const selectedModels = $('#modelSelect').val() || [];
        if (selectedModels.length === 0) return;

        const memoryState = this.memoryControls.getState();

        try {
            // Get mixed precision configuration
            const precisionConfig = this.getMixedPrecisionConfig();
            
            // Prepare request for memory analysis
            const requestData = {
                model_names: selectedModels,
                sequence_length: parseInt(document.getElementById('sequenceLength')?.value) || 2048,
                batch_size: parseInt(document.getElementById('batchSize')?.value) || 1,
                include_total_memory: memoryState.showTotalMemory,
                include_kv_cache: !precisionConfig.training,
                ...precisionConfig
            };

            // Make API request to memory analysis endpoint
            const response = await fetch(`${this.apiBaseUrl}/memory/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (response.ok) {
                const memoryData = await response.json();
                this.updateMemoryDisplay(memoryData);
            }
        } catch (error) {
            console.error('Failed to refresh memory calculations:', error);
        }
    }

    /**
     * Update memory display with new data
     */
    updateMemoryDisplay(memoryData) {
        // Update existing model cards with memory information
        Object.entries(memoryData.model_results || {}).forEach(([modelName, result]) => {
            const modelCard = document.querySelector(`[data-model="${modelName}"]`);
            if (modelCard) {
                // Update memory information in the card
                const memoryElement = modelCard.querySelector('.memory-info');
                if (memoryElement) {
                    let memoryHtml = `
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <small><strong>Parameters:</strong> ${this.formatBytes(result.parameters)}</small>
                            </div>
                            <div class="col-md-6">
                                <small><strong>Activations:</strong> ${this.formatBytes(result.activations)}</small>
                            </div>
                        </div>
                    `;
                    
                    // Add training-specific memory components if available
                    if (result.training && (result.gradients || result.optimizer_states)) {
                        memoryHtml += `
                            <div class="row">
                                <div class="col-md-6">
                                    <small><strong>Gradients:</strong> ${this.formatBytes(result.gradients || 0)}</small>
                                </div>
                                <div class="col-md-6">
                                    <small><strong>Optimizer:</strong> ${this.formatBytes(result.optimizer_states || 0)}</small>
                                </div>
                            </div>
                        `;
                    }
                    
                    // Add KV cache if available (inference only)
                    if (!result.training && result.kv_cache > 0) {
                        memoryHtml += `
                            <div class="row">
                                <div class="col-md-6">
                                    <small><strong>KV Cache:</strong> ${this.formatBytes(result.kv_cache)}</small>
                                </div>
                                <div class="col-md-6">
                                    <small><strong>Total:</strong> ${this.formatBytes(result.total)}</small>
                                </div>
                            </div>
                        `;
                    } else {
                        memoryHtml += `
                            <div class="row">
                                <div class="col-md-12">
                                    <small><strong>Total Memory:</strong> ${this.formatBytes(result.total)}</small>
                                </div>
                            </div>
                        `;
                    }
                    
                    // Add precision information
                    let precisionInfo = `Attention: ${result.attention_mechanism}`;
                    if (result.dtypes) {
                        // Mixed precision mode
                        const dtypes = result.dtypes;
                        precisionInfo += ` | Mixed: W:${dtypes.weight || 'N/A'}`;
                        if (dtypes.activation) precisionInfo += ` A:${dtypes.activation}`;
                        if (dtypes.grad) precisionInfo += ` G:${dtypes.grad}`;
                        if (dtypes.optimizer) precisionInfo += ` O:${dtypes.optimizer}`;
                        if (dtypes.kv_cache) precisionInfo += ` KV:${dtypes.kv_cache}`;
                    } else {
                        // Simple precision mode
                        precisionInfo += ` | Dtype: ${result.dtype}`;
                    }
                    
                    memoryHtml += `
                        <div class="row">
                            <div class="col-md-12">
                                <small class="text-muted">${precisionInfo}</small>
                            </div>
                        </div>
                    `;
                    
                    memoryElement.innerHTML = memoryHtml;
                }

                // Update total memory column if it exists
                const totalMemoryCol = modelCard.querySelector('.total-memory .h4');
                if (totalMemoryCol) {
                    totalMemoryCol.textContent = this.formatBytes(result.total);
                }
            }
        });
    }

    /**
     * Update analysis request to include memory calculations when enabled
     */
    updateAnalysisRequestForMemory(includeMemory) {
        // This method ensures that when memory toggle is enabled,
        // future analysis requests will include memory calculations
        this.includeMemoryInAnalysis = includeMemory;
    }

    /**
     * Update model cards with new dtype calculations
     */
    updateModelCardsWithNewDtype(dtype) {
        // If we have current analysis results, update them with new dtype
        const selectedModels = $('#modelSelect').val() || [];
        if (selectedModels.length > 0 && this.memoryState.showTotalMemory) {
            // Trigger a background memory recalculation
            this.refreshMemoryCalculationsWithDtype(dtype);
        }
    }

    /**
     * Get mixed precision configuration from UI
     */
    getMixedPrecisionConfig() {
        const enableMixedPrecision = document.getElementById('enableMixedPrecision')?.checked || false;
        const trainingMode = document.getElementById('trainingMode')?.checked || false;
        
        if (enableMixedPrecision) {
            return {
                weight_dtype: document.getElementById('weightDtype')?.value || 'fp16',
                activation_dtype: document.getElementById('activationDtype')?.value || 'fp16',
                grad_dtype: trainingMode ? (document.getElementById('gradDtype')?.value || 'fp32') : null,
                optimizer_dtype: trainingMode ? (document.getElementById('optimizerDtype')?.value || 'fp32') : null,
                kv_cache_dtype: document.getElementById('kvCacheDtype')?.value || 'fp16',
                training: trainingMode
            };
        } else {
            // Use simple precision for all components
            const dtype = document.getElementById('precision')?.value || 'fp16';
            return {
                dtype: dtype,
                weight_dtype: dtype,  // Use the same dtype for weights to ensure proper calculation
                activation_dtype: dtype,
                kv_cache_dtype: dtype,
                training: trainingMode
            };
        }
    }

    /**
     * Refresh memory calculations with mixed precision support
     */
    async refreshMemoryCalculationsWithDtype(dtype) {
        const selectedModels = $('#modelSelect').val() || [];
        if (selectedModels.length === 0) return;

        try {
            // Get mixed precision configuration
            const precisionConfig = this.getMixedPrecisionConfig();
            
            // Prepare request for memory analysis with mixed precision support
            const requestData = {
                model_names: selectedModels,
                sequence_length: parseInt(document.getElementById('sequenceLength')?.value) || 2048,
                batch_size: parseInt(document.getElementById('batchSize')?.value) || 1,
                include_total_memory: true,
                include_kv_cache: !precisionConfig.training, // KV cache only for inference
                ...precisionConfig
            };

            // Make API request to memory analysis endpoint
            const response = await fetch(`${this.apiBaseUrl}/memory/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (response.ok) {
                const memoryData = await response.json();
                this.updateMemoryDisplay(memoryData);
            }
        } catch (error) {
            console.error('Failed to refresh memory calculations with new dtype:', error);
            // Show user-friendly error message
            this.showToast('Failed to update memory calculations. Please try again.', 'warning');
        }
    }

    bindEvents() {
        // Model selection validation
        $('#modelSelect').on('change', () => {
            this.validateModelSelection();
        });

        // Preset buttons - use event delegation for more reliable binding
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-preset]')) {
                console.log('Preset button clicked via delegation:', e.target.dataset.preset);
                const preset = e.target.dataset.preset;
                this.applyPreset(preset);
            }
        });

        // Also try direct binding as backup
        const presetButtons = document.querySelectorAll('[data-preset]');
        console.log('Found preset buttons:', presetButtons.length);
        presetButtons.forEach((btn, index) => {
            console.log(`Preset button ${index}:`, btn.dataset.preset);
            btn.addEventListener('click', (e) => {
                console.log('Preset button clicked directly:', e.target.dataset.preset);
                const preset = e.target.dataset.preset;
                this.applyPreset(preset);
            });
        });

        // Parallel configuration validation
        ['tensorParallel', 'pipelineParallel', 'dataParallel', 'expertParallel'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', () => {
                    this.validateParallelConfig();
                    this.updateTotalGpus();
                });
            }
        });

        // Analysis button
        const analyzeBtn = document.getElementById('analyzeBtn');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => {
                this.startAnalysis();
            });
        }

        // Clear button
        const clearBtn = document.getElementById('clearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearSelection();
            });
        }

        // Parallel configuration toggle
        const enableParallel = document.getElementById('enableParallel');
        if (enableParallel) {
            enableParallel.addEventListener('change', () => {
                this.updateParallelConfig();
            });
        }

        // Mixed precision configuration toggle
        const enableMixedPrecision = document.getElementById('enableMixedPrecision');
        if (enableMixedPrecision) {
            enableMixedPrecision.addEventListener('change', () => {
                this.updateMixedPrecisionConfig();
            });
        }

        // Training mode toggle
        const trainingMode = document.getElementById('trainingMode');
        if (trainingMode) {
            trainingMode.addEventListener('change', () => {
                this.updateTrainingModeConfig();
            });
        }

        // Mixed precision dtype selectors
        const mixedPrecisionSelectors = ['weightDtype', 'activationDtype', 'gradDtype', 'optimizerDtype'];
        mixedPrecisionSelectors.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => {
                    this.onMixedPrecisionChange();
                });
            }
        });

        // Simple precision selector
        const precisionSelect = document.getElementById('precision');
        if (precisionSelect) {
            precisionSelect.addEventListener('change', () => {
                this.onPrecisionChange();
            });
        }
    }

    /**
     * Update mixed precision configuration visibility
     */
    updateMixedPrecisionConfig() {
        const enableMixedPrecision = document.getElementById('enableMixedPrecision');
        const simplePrecisionConfig = document.getElementById('simplePrecisionConfig');
        const mixedPrecisionConfig = document.getElementById('mixedPrecisionConfig');
        
        if (enableMixedPrecision && simplePrecisionConfig && mixedPrecisionConfig) {
            if (enableMixedPrecision.checked) {
                $(simplePrecisionConfig).hide();
                $(mixedPrecisionConfig).collapse('show');
            } else {
                $(simplePrecisionConfig).show();
                $(mixedPrecisionConfig).collapse('hide');
            }
            
            // Refresh memory calculations if enabled
            this.onPrecisionChange();
        }
    }

    /**
     * Update training mode configuration
     */
    updateTrainingModeConfig() {
        const trainingMode = document.getElementById('trainingMode');
        const kvCacheConfig = document.getElementById('kvCacheConfig');
        
        if (trainingMode && kvCacheConfig) {
            if (trainingMode.checked) {
                // Hide KV cache config for training mode
                $(kvCacheConfig).collapse('hide');
                this.showToast('Training mode enabled - KV cache disabled', 'info');
            } else {
                // Show KV cache config for inference mode
                const showMemoryToggle = document.getElementById('showTotalMemoryToggle');
                if (showMemoryToggle && showMemoryToggle.checked) {
                    $(kvCacheConfig).collapse('show');
                }
            }
            
            // Refresh memory calculations
            this.onPrecisionChange();
        }
    }

    /**
     * Handle mixed precision changes
     */
    onMixedPrecisionChange() {
        // Refresh memory calculations with new mixed precision settings
        this.refreshMemoryCalculationsWithDtype();
    }

    /**
     * Handle simple precision changes
     */
    onPrecisionChange() {
        // Refresh memory calculations with new precision settings
        this.refreshMemoryCalculationsWithDtype();
    }

    validateModelSelection() {
        const selectedModels = $('#modelSelect').val() || [];
        const validationDiv = document.getElementById('modelValidation');

        if (!validationDiv) return false;

        if (selectedModels.length === 0) {
            validationDiv.innerHTML = '';
            return false;
        }

        if (selectedModels.length === 1) {
            validationDiv.innerHTML = `
                <div class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Select multiple models for comparison
                </div>
            `;
            return true;
        }

        validationDiv.innerHTML = `
            <div class="text-success">
                <i class="fas fa-check-circle me-1"></i>
                ${selectedModels.length} models selected
            </div>
        `;

        return true;
    }

    validateParallelConfig() {
        const tp = parseInt(document.getElementById('tensorParallel')?.value) || 1;
        const pp = parseInt(document.getElementById('pipelineParallel')?.value) || 1;
        const dp = parseInt(document.getElementById('dataParallel')?.value) || 1;
        const ep = parseInt(document.getElementById('expertParallel')?.value) || 1;

        const validationDiv = document.getElementById('parallelValidation');
        if (!validationDiv) return true;

        const warnings = [];

        if (tp > 8) {
            warnings.push('Tensor parallel > 8 may have diminishing returns');
        }

        if (pp > 4) {
            warnings.push('Pipeline parallel > 4 may increase latency');
        }

        if (warnings.length > 0) {
            validationDiv.innerHTML = `
                <div class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    ${warnings.join(', ')}
                </div>
            `;
        } else {
            validationDiv.innerHTML = `
                <div class="text-success">
                    <i class="fas fa-check-circle me-1"></i>
                    Configuration looks good
                </div>
            `;
        }

        return warnings.length === 0;
    }

    updateTotalGpus() {
        const tp = parseInt(document.getElementById('tensorParallel')?.value) || 1;
        const pp = parseInt(document.getElementById('pipelineParallel')?.value) || 1;
        const dp = parseInt(document.getElementById('dataParallel')?.value) || 1;

        const totalGpus = tp * pp * dp;
        const totalGpusElement = document.getElementById('totalGpus');
        if (totalGpusElement) {
            totalGpusElement.textContent = totalGpus;
        }
    }



    clearSelection() {
        // Clear model selection
        $('#modelSelect').val(null).trigger('change');

        // Reset form values
        const sequenceLength = document.getElementById('sequenceLength');
        const batchSize = document.getElementById('batchSize');
        const precision = document.getElementById('precision');

        if (sequenceLength) sequenceLength.value = 2048;
        if (batchSize) batchSize.value = 1;
        if (precision) precision.value = 'fp16';

        // Reset parallel config
        const enableParallel = document.getElementById('enableParallel');
        if (enableParallel) {
            enableParallel.checked = false;
            $('#parallelConfig').collapse('hide');
        }

        ['tensorParallel', 'pipelineParallel', 'dataParallel', 'expertParallel'].forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = 1;
        });

        // Reset checkboxes
        const includeShapes = document.getElementById('includeShapes');
        const includeComparison = document.getElementById('includeComparison');
        if (includeShapes) includeShapes.checked = true;
        if (includeComparison) includeComparison.checked = true;

        // Clear validation messages
        const modelValidation = document.getElementById('modelValidation');
        const parallelValidation = document.getElementById('parallelValidation');
        if (modelValidation) modelValidation.innerHTML = '';
        if (parallelValidation) parallelValidation.innerHTML = '';

        // Update total GPUs
        this.updateTotalGpus();

        // Reset memory controls
        this.resetMemoryControls();

        // Hide results
        const welcomeMessage = document.getElementById('welcomeMessage');
        const analysisResults = document.getElementById('analysisResults');
        if (welcomeMessage) welcomeMessage.style.display = 'block';
        if (analysisResults) analysisResults.style.display = 'none';
    }

    /**
     * Reset memory controls to default state
     */
    resetMemoryControls() {
        // Reset memory state
        this.memoryState = {
            showTotalMemory: false,
            kvCacheDtype: 'fp16',
            minSequenceLength: 512,
            maxSequenceLength: 32768,
            supportedDtypes: this.memoryState.supportedDtypes // Preserve loaded dtypes
        };

        // Reset UI elements
        const memoryToggle = document.getElementById('showTotalMemoryToggle');
        const kvDtypeSelect = document.getElementById('kvCacheDtype');
        const minSeqLength = document.getElementById('minSequenceLength');
        const maxSeqLength = document.getElementById('maxSequenceLength');

        if (memoryToggle) memoryToggle.checked = false;
        if (kvDtypeSelect) kvDtypeSelect.value = 'fp16';
        if (minSeqLength) minSeqLength.value = 512;
        if (maxSeqLength) maxSeqLength.value = 32768;

        // Update visibility
        this.updateMemoryControlsVisibility();

        // Clear validation
        const validationDiv = document.getElementById('sequenceLengthValidation');
        if (validationDiv) validationDiv.innerHTML = '';

        // Remove any existing KV growth chart
        const chartContainer = document.getElementById('kvGrowthChartContainer');
        if (chartContainer) {
            chartContainer.remove();
        }

        // Reset memory controls if available
        if (this.memoryControls && typeof this.memoryControls.setState === 'function') {
            this.memoryControls.setState({
                showTotalMemory: false,
                kvCacheDtype: 'fp16',
                minSequenceLength: 512,
                maxSequenceLength: 32768,
                sequenceLengthStep: 1024
            });
        }
    }

    async startAnalysis() {
        const selectedModels = $('#modelSelect').val() || [];

        if (selectedModels.length === 0) {
            this.showError('Please select at least one model to analyze.');
            return;
        }

        // Get mixed precision configuration
        const precisionConfig = this.getMixedPrecisionConfig();
        
        // Prepare request data
        const requestData = {
            model_names: selectedModels,
            sequence_length: parseInt(document.getElementById('sequenceLength')?.value) || 2048,
            batch_size: parseInt(document.getElementById('batchSize')?.value) || 1,
            precision: document.getElementById('precision')?.value || 'fp16',
            include_shapes: document.getElementById('includeShapes')?.checked || true,
            include_comparison: document.getElementById('includeComparison')?.checked || true,
            ...precisionConfig
        };

        // Add memory analysis configuration if enabled
        if (this.memoryState && this.memoryState.showTotalMemory) {
            requestData.include_memory_analysis = true;
            requestData.include_kv_cache = !precisionConfig.training; // KV cache only for inference
            requestData.memory_analysis_config = {
                include_kv_cache: !precisionConfig.training,
                include_attention_mechanism: true,
                dtype: precisionConfig.kv_cache_dtype || precisionConfig.dtype || 'fp16'
            };
        }

        // Add parallel configuration if enabled
        const enableParallel = document.getElementById('enableParallel');
        if (enableParallel && enableParallel.checked) {
            requestData.parallel_config = {
                tensor_parallel_size: parseInt(document.getElementById('tensorParallel')?.value) || 1,
                pipeline_parallel_size: parseInt(document.getElementById('pipelineParallel')?.value) || 1,
                data_parallel_size: parseInt(document.getElementById('dataParallel')?.value) || 1,
                expert_parallel_size: parseInt(document.getElementById('expertParallel')?.value) || 1
            };
        }

        try {
            // Show progress
            this.showProgress('Starting analysis...', 0);

            // Disable analyze button
            const analyzeBtn = document.getElementById('analyzeBtn');
            if (analyzeBtn) {
                analyzeBtn.disabled = true;
                analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
            }

            // Make API request
            const response = await fetch(`${this.apiBaseUrl}/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            const results = await response.json();
            this.currentAnalysisId = results.request_id;

            // Hide progress and show results
            this.hideProgress();
            this.displayResults(results);

        } catch (error) {
            console.error('Analysis failed:', error);
            this.hideProgress();
            this.showError(`Analysis failed: ${error.message}`);
        } finally {
            // Re-enable analyze button
            const analyzeBtn = document.getElementById('analyzeBtn');
            if (analyzeBtn) {
                analyzeBtn.disabled = false;
                analyzeBtn.innerHTML = '<i class="fas fa-play me-2"></i>Analyze Models';
            }
        }
    }

    showProgress(message, progress) {
        const progressDiv = document.getElementById('analysisProgress');
        const progressText = document.getElementById('progressText');
        const progressBar = document.getElementById('progressBar');

        if (progressText) progressText.textContent = message;
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }

        if (progressDiv) progressDiv.style.display = 'block';
    }

    hideProgress() {
        const progressDiv = document.getElementById('analysisProgress');
        if (progressDiv) progressDiv.style.display = 'none';
    }

    displayResults(results) {
        // Hide welcome message
        const welcomeMessage = document.getElementById('welcomeMessage');
        if (welcomeMessage) welcomeMessage.style.display = 'none';

        // Show results container
        const resultsContainer = document.getElementById('analysisResults');
        if (resultsContainer) {
            resultsContainer.style.display = 'block';
        }

        // Display individual model results
        this.displayIndividualResults(results.results);

        // Display comparison results if available
        if (results.comparison) {
            this.displayComparisonResults(results.comparison);
        }

        // Show interactive controls if hardware analysis is enabled
        const hardwareAnalysisEnabled = document.getElementById('enableHardwareAnalysis')?.checked;
        if (hardwareAnalysisEnabled && this.selectedHardware) {
            this.showInteractiveControls();
            this.showAdvancedVisualizationControls();
        }

        // Update roofline visualization with results
        this.updateRooflineWithAnalysisResults(results);
        
        // Update timing dashboard with results
        this.updateTimingDashboardWithResults(results);
        
        // Show success message
        this.showToast(`Analysis completed in ${results.execution_time.toFixed(2)} seconds`, 'success');
    }

    displayIndividualResults(results) {
        const container = document.getElementById('individualResults');
        if (!container) return;

        container.innerHTML = '';

        Object.entries(results).forEach(([modelName, metrics]) => {
            const card = this.createModelCard(modelName, metrics);
            container.appendChild(card);
        });
    }



    displayComparisonResults(comparison) {
        const container = document.getElementById('comparisonResults');
        if (!container) return;

        const showMemory = this.memoryState?.showTotalMemory || false;

        container.innerHTML = `
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Model Comparison
                    </h5>
                    ${showMemory ? `
                        <small class="text-muted">
                            Memory calculations using ${this.memoryState?.kvCacheDtype?.toUpperCase() || 'FP16'} precision for KV cache
                        </small>
                    ` : ''}
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Model</th>
                                    <th>Parameters</th>
                                    <th>FLOPs/Token</th>
                                    <th>Activated Params/Token</th>
                                    <th style="display: ${showMemory ? '' : 'none'};">Total Memory</th>
                                    ${showMemory ? '<th>KV Cache</th>' : ''}
                                    ${showMemory ? '<th>Attention</th>' : ''}
                                </tr>
                            </thead>
                            <tbody>
                                ${comparison.models.map((model, index) => `
                                    <tr>
                                        <td>
                                            <strong>${model.split('/').pop()}</strong>
                                            <br><small class="text-muted">${model}</small>
                                        </td>
                                        <td>${this.formatNumber(comparison.metrics.total_params[index])}</td>
                                        <td>${this.formatNumber(comparison.metrics.flops_per_token[index])}</td>
                                        <td>${this.formatNumber(comparison.metrics.active_params_per_token?.[index] || comparison.metrics.total_params[index])}</td>
                                        <td style="display: ${showMemory ? '' : 'none'};">
                                            ${this.formatBytes(comparison.metrics.memory_total[index])}
                                        </td>
                                        ${showMemory ? `
                                            <td>${this.formatBytes(comparison.metrics.kv_cache_memory?.[index] || 0)}</td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    ${comparison.metrics.attention_mechanism?.[index] || 'Unknown'}
                                                </span>
                                            </td>
                                        ` : ''}
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    ${showMemory ? `
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                MHA: Multi-Head Attention, GQA: Grouped Query Attention, MLA: Multi-Layer Attention
                            </small>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    formatNumber(num) {
        if (num >= 1e12) {
            return (num / 1e12).toFixed(1) + 'T';
        } else if (num >= 1e9) {
            return (num / 1e9).toFixed(1) + 'B';
        } else if (num >= 1e6) {
            return (num / 1e6).toFixed(1) + 'M';
        } else if (num >= 1e3) {
            return (num / 1e3).toFixed(1) + 'K';
        }
        return num.toString();
    }

    formatBytes(bytes) {
        if (bytes >= 1024 ** 4) {
            return (bytes / (1024 ** 4)).toFixed(1) + ' TB';
        } else if (bytes >= 1024 ** 3) {
            return (bytes / (1024 ** 3)).toFixed(1) + ' GB';
        } else if (bytes >= 1024 ** 2) {
            return (bytes / (1024 ** 2)).toFixed(1) + ' MB';
        } else if (bytes >= 1024) {
            return (bytes / 1024).toFixed(1) + ' KB';
        }
        return bytes + ' B';
    }

    showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        if (errorMessage) {
            errorMessage.textContent = message;
            const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
            errorModal.show();
        } else {
            alert(message); // Fallback
        }
    }

    showToast(message, type = 'info') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        // Add to page
        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '1055';
            document.body.appendChild(toastContainer);
        }

        toastContainer.appendChild(toast);

        // Show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // Remove from DOM after hiding
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    /**
     * Enhanced error handling for KV growth API failures with user-friendly messages
     */
    handleKvGrowthApiError(error) {
        let title = 'KV Growth Analysis Failed';
        let message = error.message;
        let suggestions = [];

        // Categorize errors and provide helpful suggestions
        if (error.message.includes('timeout') || error.message.includes('timed out')) {
            title = 'Request Timeout';
            message = 'The analysis is taking longer than expected.';
            suggestions = [
                'Try reducing the sequence length range',
                'Increase the step size to generate fewer data points',
                'Select fewer models for analysis',
                'Check your internet connection'
            ];
        } else if (error.message.includes('sequence length') || error.message.includes('range')) {
            title = 'Invalid Sequence Length';
            message = 'The sequence length range is invalid.';
            suggestions = [
                'Ensure minimum is less than maximum',
                'Keep range between 512 and 32768 tokens',
                'Check that the range is larger than the step size'
            ];
        } else if (error.message.includes('data type') || error.message.includes('dtype')) {
            title = 'Data Type Error';
            message = 'The selected data type is not supported.';
            suggestions = [
                'Select a supported data type (FP16, BF16, FP32, INT8)',
                'Refresh the page if data types are not loading properly'
            ];
        } else if (error.message.includes('model')) {
            title = 'Model Error';
            message = 'There was an issue with the selected models.';
            suggestions = [
                'Verify all selected models are valid',
                'Try with a single model first',
                'Check model names for typos',
                'Some models may not support KV cache analysis'
            ];
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
            title = 'Network Error';
            message = 'Unable to connect to the server.';
            suggestions = [
                'Check your internet connection',
                'Refresh the page and try again',
                'The server may be temporarily unavailable'
            ];
        } else if (error.message.includes('500') || error.message.includes('server error')) {
            title = 'Server Error';
            message = 'The server encountered an error during analysis.';
            suggestions = [
                'Try again with a smaller sequence length range',
                'Select fewer models for analysis',
                'Contact support if the problem persists'
            ];
        } else if (error.message.includes('429') || error.message.includes('rate limit')) {
            title = 'Rate Limited';
            message = 'Too many requests have been made.';
            suggestions = [
                'Wait a moment before trying again',
                'Reduce the frequency of analysis requests'
            ];
        }

        // Show user-friendly error with suggestions
        this.showUserFriendlyError(title, message, suggestions);

        // Also show a toast for immediate feedback
        this.showToast(`${title}: ${message}`, 'error');
    }

    /**
     * Show user-friendly error modal with suggestions
     */
    showUserFriendlyError(title, message, suggestions = []) {
        // Create or get error modal
        let errorModal = document.getElementById('userFriendlyErrorModal');
        if (!errorModal) {
            errorModal = this.createUserFriendlyErrorModal();
        }

        // Update modal content
        const modalTitle = errorModal.querySelector('.modal-title');
        const modalBody = errorModal.querySelector('.modal-body');

        if (modalTitle) {
            modalTitle.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${title}`;
        }

        let bodyContent = `<p class="mb-3">${message}</p>`;

        if (suggestions.length > 0) {
            bodyContent += `
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-lightbulb me-2"></i>Suggestions to resolve this issue:
                    </h6>
                    <ul class="mb-0">
                        ${suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        // Add troubleshooting section
        bodyContent += `
            <div class="mt-3">
                <h6><i class="fas fa-tools me-2"></i>Troubleshooting:</h6>
                <div class="btn-group-vertical d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                        <i class="fas fa-refresh me-2"></i>Refresh Page
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="clearMemorySettings">
                        <i class="fas fa-eraser me-2"></i>Reset Memory Settings
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" id="showDebugInfo">
                        <i class="fas fa-bug me-2"></i>Show Debug Information
                    </button>
                </div>
            </div>
        `;

        if (modalBody) {
            modalBody.innerHTML = bodyContent;
        }

        // Bind troubleshooting buttons
        this.bindTroubleshootingButtons(errorModal);

        // Show modal
        const bsModal = new bootstrap.Modal(errorModal);
        bsModal.show();
    }

    /**
     * Create user-friendly error modal
     */
    createUserFriendlyErrorModal() {
        const modal = document.createElement('div');
        modal.id = 'userFriendlyErrorModal';
        modal.className = 'modal fade';
        modal.setAttribute('tabindex', '-1');
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">Error</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body"></div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" id="retryLastOperation">
                            <i class="fas fa-redo me-2"></i>Try Again
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        return modal;
    }

    /**
     * Bind troubleshooting button events
     */
    bindTroubleshootingButtons(modal) {
        // Clear memory settings button
        const clearSettingsBtn = modal.querySelector('#clearMemorySettings');
        if (clearSettingsBtn) {
            clearSettingsBtn.addEventListener('click', () => {
                this.resetMemorySettings();
                this.showToast('Memory settings have been reset to defaults', 'success');
            });
        }

        // Show debug info button
        const debugInfoBtn = modal.querySelector('#showDebugInfo');
        if (debugInfoBtn) {
            debugInfoBtn.addEventListener('click', () => {
                this.showDebugInformation();
            });
        }

        // Retry button
        const retryBtn = modal.querySelector('#retryLastOperation');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) bsModal.hide();

                // Retry the KV growth chart generation
                setTimeout(() => {
                    this.showKvGrowthChart();
                }, 500);
            });
        }
    }

    /**
     * Reset memory settings to defaults
     */
    resetMemorySettings() {
        this.memoryState = {
            showTotalMemory: false,
            kvCacheDtype: 'fp16',
            minSequenceLength: 512,
            maxSequenceLength: 32768,
            supportedDtypes: ['fp16', 'bf16', 'fp32', 'int8']
        };

        // Update UI elements
        const memoryToggle = document.getElementById('showTotalMemoryToggle');
        const kvDtypeSelect = document.getElementById('kvCacheDtype');
        const minSeqLength = document.getElementById('minSequenceLength');
        const maxSeqLength = document.getElementById('maxSequenceLength');

        if (memoryToggle) memoryToggle.checked = false;
        if (kvDtypeSelect) kvDtypeSelect.value = 'fp16';
        if (minSeqLength) minSeqLength.value = 512;
        if (maxSeqLength) maxSeqLength.value = 32768;

        // Update visibility
        this.updateMemoryControlsVisibility();
        this.validateSequenceLengthRange();
    }

    /**
     * Show debug information for troubleshooting
     */
    showDebugInformation() {
        const debugInfo = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            memoryState: this.memoryState,
            selectedModels: $('#modelSelect').val() || [],
            supportedModels: Object.keys(this.supportedModels.architecture_info || {}),
            chartJsAvailable: typeof Chart !== 'undefined',
            jqueryAvailable: typeof $ !== 'undefined',
            bootstrapAvailable: typeof bootstrap !== 'undefined',
            memoryControlsAvailable: typeof MemoryControls !== 'undefined',
            currentUrl: window.location.href,
            localStorage: this.getLocalStorageInfo()
        };

        // Create debug modal
        const debugModal = document.createElement('div');
        debugModal.className = 'modal fade';
        debugModal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-bug me-2"></i>Debug Information
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="copyDebugInfo">
                                <i class="fas fa-copy me-2"></i>Copy to Clipboard
                            </button>
                        </div>
                        <pre class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"><code id="debugInfoContent">${JSON.stringify(debugInfo, null, 2)}</code></pre>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(debugModal);

        // Bind copy button
        const copyBtn = debugModal.querySelector('#copyDebugInfo');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                const debugText = JSON.stringify(debugInfo, null, 2);
                navigator.clipboard.writeText(debugText).then(() => {
                    this.showToast('Debug information copied to clipboard', 'success');
                }).catch(() => {
                    this.showToast('Failed to copy debug information', 'error');
                });
            });
        }

        // Show modal
        const bsModal = new bootstrap.Modal(debugModal);
        bsModal.show();

        // Clean up modal after hiding
        debugModal.addEventListener('hidden.bs.modal', () => {
            debugModal.remove();
        });
    }

    /**
     * Get localStorage information for debugging
     */
    getLocalStorageInfo() {
        try {
            const info = {
                available: typeof Storage !== 'undefined',
                itemCount: localStorage.length,
                items: {}
            };

            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('llm_metrics_')) {
                    info.items[key] = localStorage.getItem(key);
                }
            }

            return info;
        } catch (error) {
            return { available: false, error: error.message };
        }
    }



    /**
     * Apply model preset
     */
    applyPreset(preset) {
        console.log('applyPreset called with:', preset);
        console.log('Available presets:', Object.keys(this.modelPresets));
        const models = this.modelPresets[preset];
        console.log('Models for preset:', models);
        if (models) {
            $('#modelSelect').val(models).trigger('change');
            this.showToast(`Applied ${preset} preset with ${models.length} models`, 'success');
        } else {
            console.error('No models found for preset:', preset);
        }
    }



    /**
     * Update parallel configuration visibility
     */
    updateParallelConfig() {
        const enableParallel = document.getElementById('enableParallel');
        const parallelConfig = document.getElementById('parallelConfig');

        if (enableParallel && parallelConfig) {
            if (enableParallel.checked) {
                $(parallelConfig).collapse('show');
            } else {
                $(parallelConfig).collapse('hide');
            }
        }
        this.updateTotalGpus();
    }

    /**
     * Update total GPU count
     */
    updateTotalGpus() {
        const tp = parseInt(document.getElementById('tensorParallel')?.value) || 1;
        const pp = parseInt(document.getElementById('pipelineParallel')?.value) || 1;
        const dp = parseInt(document.getElementById('dataParallel')?.value) || 1;
        const ep = parseInt(document.getElementById('expertParallel')?.value) || 1;

        const total = tp * pp * dp * ep;
        const totalGpusSpan = document.getElementById('totalGpus');
        if (totalGpusSpan) {
            totalGpusSpan.textContent = total;
        }
    }



    /**
     * Display analysis results
     */
    displayResults(data) {
        try {
            this.hideWelcomeMessage();
            this.showResultsContainer();

            // Validate data structure
            if (!data) {
                throw new Error('No analysis data received');
            }

            console.log('Full analysis data received:', data);

            // Display individual model results
            if (data.results) {
                this.displayIndividualResults(data.results);
            } else {
                console.warn('No individual results data available');
            }

            // Display comparison if available
            if (data.comparison) {
                console.log('Displaying comparison results:', data.comparison);
                console.log('Comparison metrics structure:', data.comparison.metrics);
                console.log('Comparison metrics keys:', Object.keys(data.comparison.metrics || {}));
                this.displayComparisonResults(data.comparison);
            } else {
                console.log('No comparison data available');
                // Hide comparison results container if it exists
                const comparisonContainer = document.getElementById('comparisonResults');
                if (comparisonContainer) {
                    comparisonContainer.style.display = 'none';
                }
            }
        } catch (error) {
            console.error('Error displaying results:', error);
            this.showError(`Failed to display results: ${error.message}`);
        }
    }

    /**
     * Display individual model results
     */
    displayIndividualResults(results) {
        const container = document.getElementById('individualResults');
        if (!container) return;

        container.innerHTML = '';

        Object.entries(results).forEach(([modelName, metrics]) => {
            const modelCard = this.createModelCard(modelName, metrics);
            container.appendChild(modelCard);
        });
    }

    /**
     * Create model card with metrics
     */
    createModelCard(modelName, metrics) {
        const card = document.createElement('div');
        card.className = 'card mb-4';

        const shortName = modelName.split('/').pop();

        // Format numbers for display
        const totalParams = this.formatNumber(metrics.total_params);
        const flopsPerToken = this.formatNumber(metrics.flops_per_token);
        const activeParamsPerToken = this.formatNumber(metrics.active_params_per_token || metrics.total_params);

        card.innerHTML = `
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-microchip me-2"></i>
                    ${shortName}
                </h5>
                <small class="text-muted">${modelName}</small>
            </div>
            <div class="card-body">
                <!-- Main Metrics Row -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="metric-card bg-primary text-white rounded p-3 text-center">
                            <div class="metric-value">${totalParams}</div>
                            <div class="metric-label">Total Parameters</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="metric-card bg-success text-white rounded p-3 text-center">
                            <div class="metric-value">${flopsPerToken}</div>
                            <div class="metric-label">FLOPs per Token</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="metric-card bg-info text-white rounded p-3 text-center">
                            <div class="metric-value">${activeParamsPerToken}</div>
                            <div class="metric-label">Activated Parameters per Token</div>
                        </div>
                    </div>
                </div>

                <!-- Parameter Breakdown -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6>Parameter Breakdown</h6>
                        <div class="parameter-breakdown">
                            <div class="d-flex justify-content-between">
                                <span><strong>Attention:</strong></span>
                                <span>${this.formatNumber(metrics.attention_params)}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span><strong>MLP:</strong></span>
                                <span>${this.formatNumber(metrics.mlp_params)}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span><strong>Embedding:</strong></span>
                                <span>${this.formatNumber(metrics.embedding_params)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Memory Breakdown</h6>
                        <div class="memory-breakdown">
                            <div class="d-flex justify-content-between">
                                <span><strong>Parameters:</strong></span>
                                <span>${this.formatBytes(metrics.memory_params)}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span><strong>Activations:</strong></span>
                                <span>${this.formatBytes(metrics.memory_activations)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- MoE-specific metrics if available -->
                ${metrics.experts_per_token ? `
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-network-wired me-2"></i>
                                Mixture of Experts Information
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Experts per Token:</strong> ${metrics.experts_per_token}
                                </div>
                                <div class="col-md-6">
                                    <strong>Parameter Efficiency:</strong> ${((metrics.active_params_per_token / metrics.total_params) * 100).toFixed(1)}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                ` : ''}
            </div>
        `;

        return card;
    }

    /**
     * Format large numbers with appropriate suffixes
     */
    formatNumber(num) {
        // Handle null, undefined, or non-numeric values
        if (num == null || isNaN(num)) {
            return 'N/A';
        }
        
        // Convert to number if it's a string
        const numValue = typeof num === 'string' ? parseFloat(num) : num;
        
        if (isNaN(numValue)) {
            return 'N/A';
        }
        
        if (numValue >= 1e12) {
            return (numValue / 1e12).toFixed(1) + 'T';
        } else if (numValue >= 1e9) {
            return (numValue / 1e9).toFixed(1) + 'B';
        } else if (numValue >= 1e6) {
            return (numValue / 1e6).toFixed(1) + 'M';
        } else if (numValue >= 1e3) {
            return (numValue / 1e3).toFixed(1) + 'K';
        } else {
            return numValue.toString();
        }
    }

    /**
     * Format bytes with appropriate suffixes
     */
    formatBytes(bytes) {
        // Handle null, undefined, or non-numeric values
        if (bytes == null || isNaN(bytes)) {
            return 'N/A';
        }
        
        // Convert to number if it's a string
        const bytesValue = typeof bytes === 'string' ? parseFloat(bytes) : bytes;
        
        if (isNaN(bytesValue)) {
            return 'N/A';
        }
        
        if (bytesValue >= 1024 ** 4) {
            return (bytesValue / (1024 ** 4)).toFixed(1) + ' TB';
        } else if (bytesValue >= 1024 ** 3) {
            return (bytesValue / (1024 ** 3)).toFixed(1) + ' GB';
        } else if (bytesValue >= 1024 ** 2) {
            return (bytesValue / (1024 ** 2)).toFixed(1) + ' MB';
        } else if (bytesValue >= 1024) {
            return (bytesValue / 1024).toFixed(1) + ' KB';
        } else {
            return bytesValue + ' B';
        }
    }

    /**
     * Display comparison results
     */
    displayComparisonResults(comparison) {
        const container = document.getElementById('comparisonResults');
        if (!container) return;

        // Validate comparison data structure
        if (!comparison || !comparison.models || !Array.isArray(comparison.models)) {
            console.error('Invalid comparison data: missing models array');
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    No comparison data available. Please ensure at least 2 models are selected for analysis.
                </div>
            `;
            container.style.display = 'block';
            return;
        }

        if (!comparison.metrics) {
            console.error('Invalid comparison data: missing metrics');
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Comparison metrics are not available. Please try running the analysis again.
                </div>
            `;
            container.style.display = 'block';
            return;
        }

        // Validate that core metrics arrays exist and have correct length
        const coreMetrics = ['total_params', 'flops_per_token', 'memory_total'];
        const optionalMetrics = ['active_params_per_token', 'attention_params', 'mlp_params', 'embedding_params'];
        const modelCount = comparison.models.length;
        
        // Check core metrics (required)
        for (const metric of coreMetrics) {
            if (!comparison.metrics[metric] || !Array.isArray(comparison.metrics[metric])) {
                console.error(`Invalid comparison data: missing or invalid ${metric} array`);
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Incomplete comparison metrics (missing ${metric}). Please try running the analysis again.
                    </div>
                `;
                container.style.display = 'block';
                return;
            }
            
            if (comparison.metrics[metric].length !== modelCount) {
                console.error(`Metric ${metric} array length (${comparison.metrics[metric].length}) doesn't match model count (${modelCount})`);
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Mismatched comparison data. Please try running the analysis again.
                    </div>
                `;
                container.style.display = 'block';
                return;
            }
        }
        
        // Validate optional metrics if they exist
        for (const metric of optionalMetrics) {
            if (comparison.metrics[metric] && Array.isArray(comparison.metrics[metric])) {
                if (comparison.metrics[metric].length !== modelCount) {
                    console.warn(`Optional metric ${metric} array length (${comparison.metrics[metric].length}) doesn't match model count (${modelCount})`);
                }
            }
        }

        container.innerHTML = `
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Model Comparison
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Model</th>
                                    <th>Total Parameters</th>
                                    <th>Active Parameters</th>
                                    <th>FLOPs per Token</th>
                                    <th>Memory per Token</th>
                                    <th>Parameter Efficiency</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${comparison.models.map((model, index) => {
                                    // Additional safety checks for each metric access using correct metric names
                                    const totalParams = (comparison.metrics['total_params'] || [])[index] ?? 0;
                                    const activeParams = (comparison.metrics['active_params_per_token'] || [])[index] ?? totalParams; // Fallback to total params for dense models
                                    const flopsPerToken = (comparison.metrics['flops_per_token'] || [])[index] ?? 0;
                                    const memoryTotal = (comparison.metrics['memory_total'] || [])[index] ?? 0;
                                    
                                    // Calculate efficiency safely
                                    const efficiency = totalParams > 0 ? ((activeParams / totalParams) * 100).toFixed(1) : '0.0';
                                    
                                    return `
                                        <tr>
                                            <td><strong>${model.split('/').pop()}</strong></td>
                                            <td>${this.formatNumber(totalParams)}</td>
                                            <td>${this.formatNumber(activeParams)}</td>
                                            <td>${this.formatNumber(flopsPerToken)}</td>
                                            <td>${this.formatBytes(memoryTotal)}</td>
                                            <td>${efficiency}%</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        container.style.display = 'block';
    }

    /**
     * Show/hide progress indicator
     */
    showProgress(show) {
        const progressDiv = document.getElementById('analysisProgress');
        const analyzeBtn = document.getElementById('analyzeBtn');

        if (progressDiv) {
            progressDiv.style.display = show ? 'block' : 'none';
        }

        if (analyzeBtn) {
            analyzeBtn.disabled = show;
            if (show) {
                analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
            } else {
                analyzeBtn.innerHTML = '<i class="fas fa-play me-2"></i>Analyze Models';
            }
        }
    }

    /**
     * Hide welcome message
     */
    hideWelcomeMessage() {
        const welcomeMessage = document.getElementById('welcomeMessage');
        if (welcomeMessage) {
            welcomeMessage.style.display = 'none';
        }
    }

    /**
     * Show results container
     */
    showResultsContainer() {
        const resultsContainer = document.getElementById('analysisResults');
        if (resultsContainer) {
            resultsContainer.style.display = 'block';
        }
    }

    /**
     * Hide results
     */
    hideResults() {
        const resultsContainer = document.getElementById('analysisResults');
        const welcomeMessage = document.getElementById('welcomeMessage');

        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
        if (welcomeMessage) {
            welcomeMessage.style.display = 'block';
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        if (errorMessage) {
            errorMessage.textContent = message;
            
            const errorModal = document.getElementById('errorModal');
            if (errorModal) {
                const bsModal = new bootstrap.Modal(errorModal);
                bsModal.show();
            }
        }
    }

    /**
     * Enhanced toast notification system with different types
     */
    showToast(message, type = 'info', duration = 5000) {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '1055';
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toastId = `toast-${Date.now()}`;
        const bgClass = this.getToastBackgroundClass(type);
        const icon = this.getToastIcon(type);

        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `toast align-items-center text-white ${bgClass} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${icon} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                        data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        toastContainer.appendChild(toast);

        // Initialize Bootstrap toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: duration
        });

        bsToast.show();

        // Remove toast element after it's hidden
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    /**
     * Get Bootstrap background class for toast type
     */
    getToastBackgroundClass(type) {
        const classes = {
            'success': 'bg-success',
            'error': 'bg-danger',
            'warning': 'bg-warning',
            'info': 'bg-info'
        };
        return classes[type] || 'bg-secondary';
    }

    /**
     * Get Font Awesome icon for toast type
     */
    getToastIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Enhanced loading state management for memory operations
     */
    showMemoryAnalysisLoadingState(operation = 'Analyzing memory requirements...') {
        const loadingState = {
            id: `loading-${Date.now()}`,
            originalElements: new Map(),
            operation: operation
        };

        // Show loading on relevant buttons
        const buttons = [
            document.getElementById('showKvGrowthBtn'),
            document.getElementById('analyzeBtn')
        ];

        buttons.forEach(btn => {
            if (btn) {
                loadingState.originalElements.set(btn, {
                    innerHTML: btn.innerHTML,
                    disabled: btn.disabled
                });

                btn.disabled = true;
                btn.innerHTML = `
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    ${operation}
                `;
            }
        });

        // Show progress in validation area
        const validationDiv = document.getElementById('sequenceLengthValidation');
        if (validationDiv) {
            loadingState.originalElements.set(validationDiv, {
                innerHTML: validationDiv.innerHTML
            });

            validationDiv.innerHTML = `
                <div class="alert alert-info py-2 mb-0">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    ${operation}
                </div>
            `;
        }

        return loadingState;
    }

    /**
     * Hide memory analysis loading state
     */
    hideMemoryAnalysisLoadingState(loadingState) {
        if (!loadingState) return;

        // Restore original element states
        loadingState.originalElements.forEach((originalState, element) => {
            if (element && originalState) {
                if (originalState.innerHTML !== undefined) {
                    element.innerHTML = originalState.innerHTML;
                }
                if (originalState.disabled !== undefined) {
                    element.disabled = originalState.disabled;
                }
            }
        });
    }

    /**
     * Validate dtype selection against supported types
     */
    validateKvCacheDtype(dtype) {
        if (!dtype || typeof dtype !== 'string') {
            this.showToast('Invalid data type format', 'error');
            return false;
        }

        const normalizedDtype = dtype.toLowerCase();
        const isSupported = this.memoryState.supportedDtypes.includes(normalizedDtype);

        if (!isSupported) {
            this.showToast(
                `Unsupported data type "${dtype}". Supported types: ${this.memoryState.supportedDtypes.join(', ')}`,
                'error'
            );
            return false;
        }

        // Check for compatibility warnings
        const warnings = this.getDtypeCompatibilityWarnings(normalizedDtype);
        if (warnings.length > 0) {
            this.showToast(warnings.join('; '), 'warning');
        }

        return true;
    }

    /**
     * Get compatibility warnings for dtype
     */
    getDtypeCompatibilityWarnings(dtype) {
        const warnings = [];

        if (dtype === 'int8') {
            warnings.push('INT8 may have reduced precision for some models');
        }

        if (dtype === 'fp32') {
            warnings.push('FP32 will use significantly more memory than FP16/BF16');
        }

        if (dtype === 'bf16') {
            warnings.push('BF16 may not be supported on all hardware');
        }

        return warnings;
    }
    
    initializeSectionSwitching() {
        // Initialize section switching functionality
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-section]')) {
                e.preventDefault();
                const section = e.target.getAttribute('data-section');
                this.switchToSection(section);
            }
        });
        
        // Initialize with dashboard section
        this.switchToSection('dashboard');
    }
    
    switchToSection(sectionName) {
        // Hide all sections
        const sections = [
            'container-fluid',
            'hardware-comparison-section'
        ];
        
        sections.forEach(section => {
            const element = document.querySelector(`.${section}`) || document.getElementById(section);
            if (element) {
                element.style.display = 'none';
            }
        });
        
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // Show selected section and update nav
        switch (sectionName) {
            case 'dashboard':
                const dashboardSection = document.querySelector('.container-fluid');
                if (dashboardSection) {
                    dashboardSection.style.display = 'block';
                }
                document.querySelector('.nav-link[href="#dashboard"]')?.classList.add('active');
                break;
                
            case 'hardware-comparison':
                const comparisonSection = document.getElementById('hardware-comparison-section');
                if (comparisonSection) {
                    comparisonSection.style.display = 'block';
                }
                document.querySelector('.nav-link[data-section="hardware-comparison"]')?.classList.add('active');
                break;
        }
    }
}

// Global function for section switching
function toggleSection(sectionName) {
    if (window.dashboard) {
        window.dashboard.switchToSection(sectionName);
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new LLMMetricsDashboard();
});




// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing dashboard...');
    window.dashboard = new LLMMetricsDashboard();
});